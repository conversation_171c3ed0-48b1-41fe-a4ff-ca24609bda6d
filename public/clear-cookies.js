// Clear all cookies and local storage
function clearAllCookies() {
  // Clear all cookies
  document.cookie.split(";").forEach(function(c) { 
    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
  });
  
  // Clear local storage
  localStorage.clear();
  
  // Clear session storage
  sessionStorage.clear();
  
  console.log('All cookies and storage cleared');
  
  // Reload the page
  window.location.reload();
}

// Auto-run if this script is loaded
if (typeof window !== 'undefined') {
  clearAllCookies();
}
