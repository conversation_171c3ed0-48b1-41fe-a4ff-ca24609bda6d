// Database types for Positive7 Tourism Website

export type UserRole = 'customer' | 'admin';
export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed';
export type TripDifficulty = 'easy' | 'moderate' | 'challenging' | 'extreme';
export type InquiryStatus = 'new' | 'in_progress' | 'resolved' | 'closed';

export interface User {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  date_of_birth?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  role: UserRole;
  profile_image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Trip {
  id: string;
  title: string;
  slug: string;
  description?: string;
  detailed_description?: string;
  destination: string;
  days: number;
  nights: number;
  min_age?: number;
  max_age?: number;
  price_per_person: number;
  commercial_price?: number;
  difficulty: TripDifficulty;
  inclusions?: string[];
  exclusions?: string[];
  itinerary?: any;
  featured_image_url?: string;
  is_trek: boolean;
  is_active: boolean;
  is_featured: boolean;
  available_from?: string;
  available_to?: string;
  category?: string;
  mode_of_travel?: string;
  pickup_location?: string;
  drop_location?: string;
  property_used?: string;
  activities?: string[];
  optional_activities?: string[];
  benefits?: string[];
  safety_supervision?: string[];
  things_to_carry?: string[];
  available_dates?: string[];
  special_notes?: string[];
  payment_terms?: string;
  cancellation_policy?: any;
  created_at: string;
  updated_at: string;
}

export interface TripItinerary {
  days: TripDay[];
}

export interface TripDay {
  day: number;
  title: string;
  description: string;
  activities: string[];
  accommodation?: string;
  meals?: string[];
  location?: {
    name: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
}

export interface Booking {
  id: string;
  user_id: string;
  trip_id: string;
  booking_reference: string;
  number_of_participants: number;
  total_amount: number;
  status: BookingStatus;
  special_requirements?: string;
  emergency_contact?: EmergencyContact;
  participants?: Participant[];
  payment_details?: PaymentDetails;
  booking_date: string;
  created_at: string;
  updated_at: string;
  // Relations
  user?: User;
  trip?: Trip;
}

export interface EmergencyContact {
  name: string;
  phone: string;
  relationship: string;
}

export interface Participant {
  name: string;
  age: number;
  gender?: string;
  dietary_requirements?: string;
  medical_conditions?: string;
  emergency_contact?: EmergencyContact;
}

export interface PaymentDetails {
  method: 'razorpay' | 'stripe' | 'bank_transfer';
  transaction_id?: string;
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded';
  amount_paid: number;
  payment_date?: string;
  gateway_response?: any;
}

// Testimonial interface for UI only (no database table)
export interface Testimonial {
  id: string;
  name: string;
  role?: string; // changed from user_id
  rating: number;
  title?: string;
  content: string;
  image_url?: string;
  is_approved?: boolean;
  is_featured?: boolean;
  // No database relationship fields
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featured_image_url?: string;
  author_id?: string;
  category?: string;
  tags?: string[];
  is_published: boolean;
  published_at?: string;
  seo_title?: string;
  seo_description?: string;
  created_at: string;
  updated_at: string;
  // Relations
  author?: User;
}

export interface Inquiry {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subject?: string;
  message: string;
  inquiry_type?: string;
  trip_id?: string;
  status: InquiryStatus;
  admin_notes?: string;
  responded_at?: string;
  created_at: string;
  updated_at: string;
  // Relations
  trip?: Trip;
}

export interface NewsletterSubscription {
  id: string;
  email: string;
  name?: string;
  is_active: boolean;
  subscribed_at: string;
  unsubscribed_at?: string;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Filter and search types
export interface TripFilters {
  destination?: string;
  difficulty?: TripDifficulty;
  minPrice?: number;
  maxPrice?: number;
  minDuration?: number;
  maxDuration?: number;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface BookingFilters {
  status?: BookingStatus;
  dateFrom?: string;
  dateTo?: string;
  userId?: string;
  tripId?: string;
}

// Form types
export interface CreateTripData {
  title: string;
  slug: string;
  description?: string;
  detailed_description?: string;
  destination: string;
  days: number;
  nights: number;
  min_age?: number;
  max_age?: number;
  price_per_person: number;
  difficulty: TripDifficulty;
  inclusions?: string[];
  exclusions?: string[];
  itinerary?: TripItinerary;
  featured_image_url?: string;
  is_trek?: boolean;
  is_active?: boolean;
  is_featured?: boolean;
  available_from?: string;
  available_to?: string;
}

export interface CreateBookingData {
  trip_id: string;
  number_of_participants: number;
  special_requirements?: string;
  emergency_contact: EmergencyContact;
  participants: Participant[];
  booking_date: string;
}

export interface CreateTestimonialData {
  trip_id?: string;
  name: string;
  email?: string;
  rating: number;
  title?: string;
  content: string;
  image_url?: string;
}

export interface CreateInquiryData {
  name: string;
  email: string;
  phone?: string;
  subject?: string;
  message: string;
  inquiry_type?: string;
  trip_id?: string;
}
