export interface TripPhotoDetails {
  id: string;
  trip_name: string;
  trip_description: string | null;
  featured_image_url: string | null;
  access_password: string | null;
  google_drive_link: string | null;
  drive_folder_id: string | null;
  created_at: string;
  updated_at: string;
}

export interface TripPhotoDetailsFormData extends Omit<TripPhotoDetails, 'id' | 'created_at' | 'updated_at' | 'drive_folder_id'> {
  // Form data doesn't need id, created_at, updated_at as they are handled by the server
  // drive_folder_id is derived from google_drive_link
} 