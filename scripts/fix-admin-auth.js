/**
 * <PERSON><PERSON><PERSON> to fix admin user authentication
 * This will ensure the user has proper auth credentials in Supabase
 * 
 * Usage: node scripts/fix-admin-auth.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const adminEmail = process.env.ADMIN_EMAIL;
const adminPassword = process.env.ADMIN_PASSWORD;

if (!supabaseUrl || !supabaseServiceKey || !adminEmail || !adminPassword) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixAdminAuth() {
  try {
    console.log('🔧 Fixing admin user authentication...');
    console.log(`Email: ${adminEmail}`);

    // First, let's check if the user exists in auth.users
    console.log('\n1. Checking existing users in Supabase Auth...');
    
    // List all users to see what we have
    const { data: users, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      console.error('Error listing users:', listError);
      throw listError;
    }

    console.log(`Found ${users.users.length} users in auth system`);
    
    const existingUser = users.users.find(user => user.email === adminEmail);
    
    if (existingUser) {
      console.log(`✅ User ${adminEmail} exists in auth with ID: ${existingUser.id}`);
      console.log(`   Email confirmed: ${existingUser.email_confirmed_at ? 'Yes' : 'No'}`);
      console.log(`   Created at: ${existingUser.created_at}`);
      
      // Update the user's password
      console.log('\n2. Updating user password...');
      const { data: updateData, error: updateError } = await supabase.auth.admin.updateUserById(
        existingUser.id,
        {
          password: adminPassword,
          email_confirm: true
        }
      );

      if (updateError) {
        console.error('Error updating user password:', updateError);
        throw updateError;
      }

      console.log('✅ Password updated successfully');

    } else {
      console.log(`❌ User ${adminEmail} not found in auth system`);
      console.log('\n2. Creating user in Supabase Auth...');
      
      // Create the user in Supabase Auth
      const { data: createData, error: createError } = await supabase.auth.admin.createUser({
        email: adminEmail,
        password: adminPassword,
        email_confirm: true,
        user_metadata: {
          full_name: 'Super Administrator',
          username: 'superadmin'
        }
      });

      if (createError) {
        console.error('Error creating user:', createError);
        throw createError;
      }

      console.log(`✅ User created successfully with ID: ${createData.user.id}`);
    }

    // Check and assign super_admin role
    console.log('\n3. Checking admin roles...');
    
    const currentUserId = existingUser?.id || createData?.user?.id;
    
    if (!currentUserId) {
      throw new Error('Could not determine user ID');
    }

    // Check if user has super_admin role
    const { data: userRoles, error: rolesError } = await supabase
      .from('admin_user_roles')
      .select(`
        admin_roles (
          id,
          name
        )
      `)
      .eq('user_id', currentUserId);

    if (rolesError) {
      console.error('Error checking user roles:', rolesError);
    } else {
      const roleNames = userRoles.map(ur => ur.admin_roles?.name).filter(Boolean);
      console.log(`Current roles: ${roleNames.length > 0 ? roleNames.join(', ') : 'None'}`);
      
      if (!roleNames.includes('super_admin')) {
        console.log('\n4. Assigning super_admin role...');
        
        // Get super_admin role ID
        const { data: superAdminRole, error: roleError } = await supabase
          .from('admin_roles')
          .select('id')
          .eq('name', 'super_admin')
          .single();

        if (roleError) {
          console.error('Error finding super_admin role:', roleError);
          throw roleError;
        }

        // Assign role to user
        const { error: assignError } = await supabase
          .from('admin_user_roles')
          .insert({
            user_id: currentUserId,
            role_id: superAdminRole.id
          });

        if (assignError) {
          console.error('Error assigning super_admin role:', assignError);
          throw assignError;
        }

        console.log('✅ Super admin role assigned successfully');
      } else {
        console.log('✅ User already has super_admin role');
      }
    }

    console.log('\n🎉 Admin authentication setup completed successfully!');
    console.log('\n📋 Login Details:');
    console.log(`   URL: http://localhost:3000/admin/login`);
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Password: ${adminPassword}`);
    console.log('\n🔐 You can now login to the admin panel!');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the fix
fixAdminAuth();
