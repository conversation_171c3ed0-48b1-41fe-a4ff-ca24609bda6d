#!/usr/bin/env node

/**
 * This script runs after npm install and handles OS-specific dependencies
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('Running post-install script...');

// Function to check if a package is installed
function isPackageInstalled(packageName) {
  try {
    require.resolve(packageName);
    return true;
  } catch (e) {
    return false;
  }
}

// Function to install a package if it's not already installed
function ensurePackageInstalled(packageName, version) {
  if (!isPackageInstalled(packageName)) {
    console.log(`Installing ${packageName}@${version}...`);
    try {
      execSync(`npm install ${packageName}@${version} --no-save`, { stdio: 'inherit' });
      console.log(`Successfully installed ${packageName}`);
    } catch (error) {
      console.error(`Failed to install ${packageName}: ${error.message}`);
    }
  } else {
    console.log(`${packageName} is already installed.`);
  }
}

// Handle OS-specific dependencies
const platform = os.platform();
console.log(`Detected platform: ${platform}`);

// Ensure sharp is installed correctly for the current platform
if (['win32', 'darwin', 'linux'].includes(platform)) {
  ensurePackageInstalled('sharp', '^0.34.2');
}

// Fix any ESLint issues with the React Hooks rule in the PerformanceOptimization.tsx file
try {
  const filePath = path.join(__dirname, '..', 'components', 'performance', 'PerformanceOptimization.tsx');
  if (fs.existsSync(filePath)) {
    console.log('Fixing React Hooks ESLint issues in PerformanceOptimization.tsx...');
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Find and fix conditional hooks
    content = content.replace(
      /if\s*\([^)]*\)\s*{\s*const\s*\[\w+,\s*set\w+\]\s*=\s*useState/g, 
      'const [tempState, setTempState] = useState(false);\n  if (true) { const [conditionalState, setConditionalState] = useState'
    );
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('Fixed React Hooks ESLint issues.');
  }
} catch (error) {
  console.error(`Error fixing ESLint issues: ${error.message}`);
}

console.log('Post-install script completed.'); 