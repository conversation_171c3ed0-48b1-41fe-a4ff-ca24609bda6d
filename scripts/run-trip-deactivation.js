#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to run the trip deactivation cron job
 * 
 * Usage:
 * node scripts/run-trip-deactivation.js
 * 
 * This can be scheduled using a cron job on the server:
 * 0 0 * * * /usr/bin/node /path/to/app/scripts/run-trip-deactivation.js >> /path/to/logs/trip-deactivation.log 2>&1
 */

const https = require('https');
const http = require('http');

// Configuration
const isProduction = process.env.NODE_ENV === 'production';
const baseUrl = isProduction 
  ? process.env.NEXT_PUBLIC_SITE_URL || 'https://positive7.org'
  : 'http://localhost:3000';

const endpoint = '/api/cron/trip-deactivation';
const url = `${baseUrl}${endpoint}`;

console.log(`[${new Date().toISOString()}] Running trip deactivation cron job`);
console.log(`URL: ${url}`);

// Choose http or https module based on URL
const client = url.startsWith('https') ? https : http;

const req = client.get(url, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log(`[${new Date().toISOString()}] Status: ${res.statusCode}`);
      console.log(`Response:`, response);
      
      if (response.deactivatedTrips && response.deactivatedTrips.length > 0) {
        console.log(`Deactivated ${response.deactivatedTrips.length} trips:`);
        response.deactivatedTrips.forEach(trip => {
          console.log(`- ${trip.title} (ID: ${trip.id})`);
        });
      } else {
        console.log('No trips were deactivated');
      }
    } catch (error) {
      console.error('Error parsing response:', error);
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error(`[${new Date().toISOString()}] Error:`, error.message);
});

req.end(); 