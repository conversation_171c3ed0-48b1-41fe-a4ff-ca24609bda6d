/**
 * <PERSON><PERSON><PERSON> to create additional sample admin users
 * Run this after the main migration script
 * 
 * Usage: node scripts/create-sample-users.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Additional sample users to create
const sampleUsers = [
  {
    email: '<EMAIL>',
    password: 'Content123!',
    full_name: 'Content Manager',
    username: 'contentmanager',
    roles: ['content_manager']
  },
  {
    email: '<EMAIL>',
    password: 'Support123!',
    full_name: 'Customer Support',
    username: 'support',
    roles: ['customer_support']
  },
  {
    email: '<EMAIL>',
    password: 'Photos123!',
    full_name: 'Photo Manager',
    username: 'photomanager',
    roles: ['photo_manager']
  }
];

async function createSampleUsers() {
  try {
    console.log('Creating additional sample admin users...');
    
    const credentials = [];

    for (const userData of sampleUsers) {
      console.log(`\nProcessing user: ${userData.email}`);
      
      // Check if user already exists
      const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers();
      
      if (listError) {
        console.error(`Failed to list users: ${listError.message}`);
        continue;
      }

      const existingUser = existingUsers.users.find(user => user.email === userData.email);
      
      if (existingUser) {
        console.log(`User ${userData.email} already exists`);
        credentials.push({
          email: userData.email,
          password: userData.password,
          roles: userData.roles,
          status: 'existing'
        });
        continue;
      }

      // Create new admin user with simpler approach
      console.log(`Creating admin user: ${userData.email}`);
      
      try {
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          email: userData.email,
          password: userData.password,
          email_confirm: true
        });

        if (authError) {
          console.error(`Failed to create user ${userData.email}:`, authError.message);
          continue;
        }

        console.log(`User created successfully with ID: ${authData.user.id}`);

        // Update the admin profile
        const { error: profileError } = await supabase
          .from('admin_profiles')
          .update({
            full_name: userData.full_name,
            username: userData.username
          })
          .eq('id', authData.user.id);

        if (profileError) {
          console.warn(`Warning: Could not update profile for ${userData.email}:`, profileError.message);
        }

        // Assign roles
        await assignRolesToUser(authData.user.id, userData.roles);

        credentials.push({
          email: userData.email,
          password: userData.password,
          roles: userData.roles,
          status: 'created'
        });

      } catch (createError) {
        console.error(`Error creating user ${userData.email}:`, createError.message);
        continue;
      }
    }

    // Update credentials file
    await updateCredentialsFile(credentials);

    console.log('\n✅ Sample users creation completed!');
    console.log('📄 Credentials have been updated in admin-credentials.txt');

  } catch (error) {
    console.error('Script failed:', error.message);
    process.exit(1);
  }
}

async function assignRolesToUser(userId, roleNames) {
  // Get role IDs
  const { data: roles, error: roleError } = await supabase
    .from('admin_roles')
    .select('id, name')
    .in('name', roleNames);

  if (roleError) {
    throw new Error(`Failed to find roles: ${roleError.message}`);
  }

  if (roles.length === 0) {
    throw new Error(`No roles found for: ${roleNames.join(', ')}`);
  }

  // Assign roles to user
  const userRoleInserts = roles.map(role => ({
    user_id: userId,
    role_id: role.id
  }));

  const { error: assignError } = await supabase
    .from('admin_user_roles')
    .insert(userRoleInserts);

  if (assignError) {
    throw new Error(`Failed to assign roles: ${assignError.message}`);
  }

  console.log(`Roles assigned successfully: ${roles.map(r => r.name).join(', ')}`);
}

async function updateCredentialsFile(newCredentials) {
  const fs = require('fs');
  const path = require('path');
  
  // Read existing credentials
  const filePath = path.join(process.cwd(), 'admin-credentials.txt');
  let existingContent = '';
  
  try {
    existingContent = fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.log('No existing credentials file found, creating new one');
  }

  // Extract existing user sections
  const existingUsers = [];
  if (existingContent.includes('## SUPER_ADMIN USER')) {
    existingUsers.push({
      email: '<EMAIL>',
      password: 'pratham1',
      roles: ['super_admin'],
      status: 'existing'
    });
  }

  // Combine all credentials
  const allCredentials = [...existingUsers, ...newCredentials];

  const content = `# Admin User Credentials for Positive7 Tourism
# Generated on: ${new Date().toISOString()}
# 
# IMPORTANT: Keep this file secure and do not commit to version control!

${allCredentials.map(cred => `
## ${cred.roles.join(', ').toUpperCase()} USER
Email: ${cred.email}
Password: ${cred.password}
Roles: ${cred.roles.join(', ')}
Status: ${cred.status}
`).join('\n')}

## Login Instructions:
1. Go to: http://localhost:3000/admin/login
2. Use any of the above credentials to login
3. Each user will see different admin sections based on their role

## Role Permissions:
- super_admin: Full access to all features including user management
- content_manager: Can manage trips, blogs, and view inquiries
- customer_support: Can handle inquiries and view content
- photo_manager: Can manage trip photos and view trips

## Security Notes:
- Change these passwords in production
- Use strong passwords for production environments
- Regularly audit user access and permissions
`;

  fs.writeFileSync(filePath, content);
  console.log(`Credentials updated in: ${filePath}`);
}

// Run the script
createSampleUsers();
