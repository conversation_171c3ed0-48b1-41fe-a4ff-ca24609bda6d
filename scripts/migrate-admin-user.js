/**
 * Migration script to create admin users with different roles in Supabase Auth
 * Run this script after setting up the new authentication system
 *
 * Usage: node scripts/migrate-admin-user.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const adminEmail = process.env.ADMIN_EMAIL;
const adminPassword = process.env.ADMIN_PASSWORD;

if (!supabaseUrl || !supabaseServiceKey || !adminEmail || !adminPassword) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Sample admin users to create
const sampleUsers = [
  {
    email: adminEmail,
    password: adminPassword,
    full_name: 'Super Administrator',
    username: 'superadmin',
    roles: ['super_admin']
  },
  {
    email: '<EMAIL>',
    password: 'Content123!',
    full_name: 'Content Manager',
    username: 'contentmanager',
    roles: ['content_manager']
  },
  {
    email: '<EMAIL>',
    password: 'Support123!',
    full_name: 'Customer Support',
    username: 'support',
    roles: ['customer_support']
  },
  {
    email: '<EMAIL>',
    password: 'Photos123!',
    full_name: 'Photo Manager',
    username: 'photomanager',
    roles: ['photo_manager']
  }
];

async function migrateAdminUsers() {
  try {
    console.log('Starting admin users migration...');

    const credentials = [];

    for (const userData of sampleUsers) {
      console.log(`\nProcessing user: ${userData.email}`);

      // Check if user already exists
      const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers();

      if (listError) {
        throw new Error(`Failed to list users: ${listError.message}`);
      }

      const existingUser = existingUsers.users.find(user => user.email === userData.email);

      if (existingUser) {
        console.log(`User ${userData.email} already exists with ID: ${existingUser.id}`);

        // Check if they have admin roles
        const { data: userRoles, error: rolesError } = await supabase
          .from('admin_user_roles')
          .select('admin_roles(name)')
          .eq('user_id', existingUser.id);

        if (rolesError) {
          console.error('Error checking user roles:', rolesError);
        } else if (userRoles.length === 0) {
          console.log('User exists but has no admin roles. Assigning roles...');
          await assignRolesToUser(existingUser.id, userData.roles);
        } else {
          console.log('User already has admin roles:', userRoles.map(ur => ur.admin_roles.name));
        }

        credentials.push({
          email: userData.email,
          password: userData.password,
          roles: userData.roles,
          status: 'existing'
        });

        continue;
      }

      // Create new admin user
      console.log(`Creating admin user: ${userData.email}`);

      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        user_metadata: {
          full_name: userData.full_name,
          username: userData.username
        },
        email_confirm: true
      });

      if (authError) {
        console.error(`Failed to create user ${userData.email}:`, authError.message);
        continue;
      }

      console.log(`User created successfully with ID: ${authData.user.id}`);

      // Assign roles
      await assignRolesToUser(authData.user.id, userData.roles);

      credentials.push({
        email: userData.email,
        password: userData.password,
        roles: userData.roles,
        status: 'created'
      });
    }

    // Write credentials to file
    await writeCredentialsFile(credentials);

    console.log('\n✅ Admin users migration completed successfully!');
    console.log('📄 Credentials have been saved to admin-credentials.txt');

  } catch (error) {
    console.error('Migration failed:', error.message);
    process.exit(1);
  }
}

async function assignRolesToUser(userId, roleNames) {
  // Get role IDs
  const { data: roles, error: roleError } = await supabase
    .from('admin_roles')
    .select('id, name')
    .in('name', roleNames);

  if (roleError) {
    throw new Error(`Failed to find roles: ${roleError.message}`);
  }

  if (roles.length === 0) {
    throw new Error(`No roles found for: ${roleNames.join(', ')}`);
  }

  // Assign roles to user
  const userRoleInserts = roles.map(role => ({
    user_id: userId,
    role_id: role.id
  }));

  const { error: assignError } = await supabase
    .from('admin_user_roles')
    .insert(userRoleInserts);

  if (assignError) {
    throw new Error(`Failed to assign roles: ${assignError.message}`);
  }

  console.log(`Roles assigned successfully: ${roles.map(r => r.name).join(', ')}`);
}

async function writeCredentialsFile(credentials) {
  const fs = require('fs');
  const path = require('path');

  const content = `# Admin User Credentials for Positive7 Tourism
# Generated on: ${new Date().toISOString()}
#
# IMPORTANT: Keep this file secure and do not commit to version control!

${credentials.map(cred => `
## ${cred.roles.join(', ').toUpperCase()} USER
Email: ${cred.email}
Password: ${cred.password}
Roles: ${cred.roles.join(', ')}
Status: ${cred.status}
`).join('\n')}

## Login Instructions:
1. Go to: http://localhost:3000/admin/login
2. Use any of the above credentials to login
3. Each user will see different admin sections based on their role

## Role Permissions:
- super_admin: Full access to all features including user management
- content_manager: Can manage trips, blogs, and view inquiries
- customer_support: Can handle inquiries and view content
- photo_manager: Can manage trip photos and view trips

## Security Notes:
- Change these passwords in production
- Use strong passwords for production environments
- Regularly audit user access and permissions
`;

  const filePath = path.join(process.cwd(), 'admin-credentials.txt');
  fs.writeFileSync(filePath, content);
  console.log(`Credentials written to: ${filePath}`);
}

// Run the migration
migrateAdminUsers();
