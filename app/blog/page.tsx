import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import BlogClient from '@/components/blog/BlogClient'
import { createServerSupabase } from '@/lib/supabase-server'

export const metadata: Metadata = {
  title: 'Blog - Positive7 Educational Tours',
  description: 'Read our latest travel stories, educational insights, and adventure guides. Discover amazing destinations and travel tips from our expert team.',
  keywords: 'travel blog, educational tours, adventure stories, travel tips, Positive7, destinations'
}

export default async function BlogPage() {
  const supabase = createServerSupabase();

  // Fetch blog posts from database
  const { data: postsData, error } = await supabase
    .from('blog_posts')
    .select(`
      id,
      title,
      slug,
      excerpt,
      content,
      featured_image_url,
      category,
      tags,
      published_at,
      created_at
    `)
    .eq('is_published', true)
    .order('published_at', { ascending: false })
    .limit(20);

  if (error) {
    console.error('Error fetching blog posts:', error);
  }

  // Ensure all posts have a valid created_at value
  const currentDate = new Date().toISOString();
  const posts = postsData?.map(post => ({
    ...post,
    created_at: post.created_at || currentDate
  })) || [];

  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <BlogClient initialPosts={posts} />
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
