import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import { createServerSupabase } from '@/lib/supabase-server'
import TripDetailClient from '@/components/trips/TripDetailClient'
import { Suspense } from 'react'
import { PageLoading } from '@/components/ui/LoadingStates'
import type { Trip } from '@/types/database'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

interface TripDetailPageProps {
  params: Promise<{
    slug: string
  }>
}

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic'

// Generate metadata for the trip page
export async function generateMetadata({ params }: TripDetailPageProps): Promise<Metadata> {
  const { slug } = await params;
  const trip = await getTripBySlug(slug);
  
  if (!trip) {
    return {
      title: 'Trip Not Found | Positive7 Tourism',
      description: 'The requested trip could not be found.'
    }
  }

  const baseTitle = `${trip.title} | Educational Tour in ${trip.destination} | Positive7 Tourism`
  const description = trip.description 
    ? `${trip.description.substring(0, 160)}...` 
    : `Explore ${trip.destination} with Positive7 Tourism. ${trip.days} days educational journey with ${trip.is_trek ? 'trekking and adventure' : 'immersive learning'} experiences.`

  const keywords = [
    trip.destination,
    'educational tour',
    trip.is_trek ? 'trekking' : '',
    trip.is_trek ? 'adventure' : '',
    'travel',
    `${trip.days} days tour`,
    'student trip',
    'Positive7 Tourism',
    trip.difficulty,
    'educational journey'
  ].filter(Boolean).join(', ')

  return {
    title: baseTitle,
    description: description,
    keywords: keywords,
    openGraph: {
      title: trip.title,
      description: description,
      type: 'article',
      url: `https://positive7.org/trips/${trip.slug}`,
      images: trip.featured_image_url ? [trip.featured_image_url] : undefined
    },
    twitter: {
      card: 'summary_large_image',
      title: trip.title,
      description: description,
      images: trip.featured_image_url ? [trip.featured_image_url] : undefined
    }
  }
}

// Get trip by slug
async function getTripBySlug(slug: string) {
  const supabase = createServerSupabase()
  
  const { data: trip, error } = await supabase
    .from('trips')
    .select('*')
    .eq('slug', slug)
    .eq('is_active', true)
    .single()
    
  if (error) {
    console.error('Error fetching trip by slug:', error)
    return null
  }
  
  // Transform the database trip data to match the expected client format
  return {
    id: trip.id,
    title: trip.title,
    slug: trip.slug,
    description: trip.description || '',
    destination: trip.destination,
    days: trip.days || 0,
    nights: trip.nights || 0,
    difficulty: trip.difficulty || 'moderate',
    price_per_person: trip.price_per_person || 0,
    featured_image_url: trip.featured_image_url || '/images/fallback-trip.jpg',
    // Additional properties that might be used by the detail component
    detailed_description: trip.detailed_description || trip.description || '',
    mode_of_travel: trip.mode_of_travel || undefined,
    pickup_location: trip.pickup_location || undefined,
    drop_location: trip.drop_location || undefined,
    property_used: trip.property_used || undefined,
    min_age: trip.min_age || undefined,
    max_age: trip.max_age || undefined,
    is_trek: trip.is_trek || false,
    category: trip.category || undefined,
    itinerary: (trip.itinerary as Record<string, any>) || {},
    activities: trip.activities || [],
    optional_activities: trip.optional_activities || [],
    inclusions: trip.inclusions || [],
    exclusions: trip.exclusions || [],
    benefits: trip.benefits || [],
    safety_supervision: trip.safety_supervision || [],
    things_to_carry: trip.things_to_carry || [],
    special_notes: trip.special_notes || [],
    payment_terms: trip.payment_terms || undefined,
    cancellation_policy: trip.cancellation_policy || undefined,
    available_dates: trip.available_dates || [],
    commercial_price: trip.commercial_price || undefined,
    available_from: trip.available_from || undefined,
    available_to: trip.available_to || undefined
  }
}

// Get related trips (excluding current trip)
async function getRelatedTrips(currentTripId: string, destination: string, limit = 3) {
  const supabase = createServerSupabase()
  
  // First try to get trips with the same destination
  const { data: relatedTrips, error } = await supabase
    .from('trips')
    .select('*')
    .eq('is_active', true)
    .eq('destination', destination)
    .neq('id', currentTripId)
    .limit(limit)
    
  if (error || !relatedTrips) {
    console.error('Error fetching related trips:', error)
    return []
  }
  
  // If we didn't get enough trips, fetch more regardless of destination
  let combinedTrips = [...relatedTrips];
  if (combinedTrips.length < limit) {
    const { data: moreTrips, error: moreError } = await supabase
      .from('trips')
      .select('*')
      .eq('is_active', true)
      .neq('id', currentTripId)
      .limit(limit - combinedTrips.length)
      
    if (!moreError && moreTrips) {
      combinedTrips = [...combinedTrips, ...moreTrips]
    }
  }
  
  // Transform the results to match the Trip interface expected by TripDetailClient
  return combinedTrips.map(trip => ({
    id: trip.id,
    title: trip.title,
    slug: trip.slug,
    description: trip.description || '',
    destination: trip.destination,
    days: trip.days || 0,
    nights: trip.nights || 0,
    price_per_person: trip.price_per_person || 0,
    difficulty: trip.difficulty || 'moderate',
    featured_image_url: trip.featured_image_url || '/images/fallback-trip.jpg',
    is_trek: trip.is_trek || false,
    detailed_description: trip.description || '',
    itinerary: trip.itinerary || {},
    activities: trip.activities || [],
    optional_activities: trip.optional_activities || [],
    inclusions: trip.inclusions || [],
    exclusions: trip.exclusions || [],
    benefits: trip.benefits || [],
    safety_supervision: trip.safety_supervision || [],
    things_to_carry: trip.things_to_carry || [],
    special_notes: trip.special_notes || [],
    payment_terms: trip.payment_terms || undefined,
    cancellation_policy: trip.cancellation_policy || undefined,
    available_dates: trip.available_dates || [],
    commercial_price: trip.commercial_price || undefined,
    available_from: trip.available_from || undefined,
    available_to: trip.available_to || undefined
  }))
}

export default async function TripDetailPage({ params }: TripDetailPageProps) {
  const { slug } = await params;
  
  // Fetch trip data from the database
  const trip = await getTripBySlug(slug);
  
  // If trip not found, return 404
  if (!trip) {
    notFound();
  }
  
  // Fetch related trips
  const relatedTrips = await getRelatedTrips(trip.id, trip.destination);

  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <Suspense fallback={<PageLoading message="Loading trip details..." />}>
            <TripDetailClient trip={trip} relatedTrips={relatedTrips} />
          </Suspense>
        </div>
      </main>
      <Footer />
    </>
  );
}
