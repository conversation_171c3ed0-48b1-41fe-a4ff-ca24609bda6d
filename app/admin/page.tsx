'use client';

import { useState, useEffect } from 'react';
import {
  Map,
  FileText,
  MessageSquare,
  Users,
  TrendingUp,
  Calendar,
  CheckCircle
} from 'lucide-react';
import { useAuth } from '@/lib/hooks/useAuth';
import Link from 'next/link';
import AdminLayout from '@/components/layout/AdminLayout';

interface DashboardStats {
  totalTrips: number;
  activeTrips: number;
  totalBlogs: number;
  totalInquiries: number;
  newInquiries: number;
}

export default function AdminDashboardPage() {
  const { hasPermission, adminUser } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalTrips: 0,
    activeTrips: 0,
    totalBlogs: 0,
    totalInquiries: 0,
    newInquiries: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch trips count
        const tripsResponse = await fetch('/api/admin/trips?limit=1');
        const tripsData = await tripsResponse.json();
        
        // Fetch blogs count
        const blogsResponse = await fetch('/api/admin/blogs?limit=1');
        const blogsData = await blogsResponse.json();
        
        // Fetch inquiries count
        const inquiriesResponse = await fetch('/api/admin/inquiries?limit=1');
        const inquiriesData = await inquiriesResponse.json();
        
        setStats({
          totalTrips: tripsData.pagination?.total || 0,
          activeTrips: tripsData.pagination?.total || 0, // This would need a specific API endpoint
          totalBlogs: blogsData.pagination?.total || 0,
          totalInquiries: inquiriesData.pagination?.total || 0,
          newInquiries: inquiriesData.pagination?.total || 0 // This would need a specific API endpoint
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color,
    loading
  }: { 
    title: string; 
    value: number; 
    icon: React.ReactNode; 
    color: string;
    loading: boolean;
  }) => (
    <div className={`bg-white rounded-lg shadow-md p-6 border-l-4 ${color}`}>
      <div className="flex justify-between items-center">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          {loading ? (
            <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mt-1"></div>
          ) : (
            <p className="text-3xl font-bold text-gray-900">{value}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color.replace('border-', 'bg-').replace('-600', '-100')}`}>
          {icon}
        </div>
      </div>
    </div>
  );

  const QuickAction = ({ 
    title, 
    description, 
    icon, 
    href, 
    color 
  }: { 
    title: string; 
    description: string; 
    icon: React.ReactNode; 
    href: string; 
    color: string;
  }) => (
    <Link href={href as any} className="block">
      <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
        <div className={`p-3 rounded-full ${color} w-fit mb-4`}>
          {icon}
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    </Link>
  );

  // Filter quick actions based on permissions
  const getQuickActions = () => {
    const actions = [];

    if (hasPermission('trips', 'create')) {
      actions.push({
        title: "Add New Trip",
        description: "Create a new trip with itinerary and details",
        icon: <Map className="w-6 h-6 text-blue-600" />,
        href: "/admin/trips/new",
        color: "bg-blue-100"
      });
    }

    if (hasPermission('blog', 'create')) {
      actions.push({
        title: "Write New Blog",
        description: "Create a new blog post for your website",
        icon: <FileText className="w-6 h-6 text-purple-600" />,
        href: "/admin/blogs/new",
        color: "bg-purple-100"
      });
    }

    if (hasPermission('inquiries', 'read')) {
      actions.push({
        title: "View Inquiries",
        description: "Check and respond to customer inquiries",
        icon: <MessageSquare className="w-6 h-6 text-amber-600" />,
        href: "/admin/inquiries",
        color: "bg-amber-100"
      });
    }

    if (hasPermission('users', 'read')) {
      actions.push({
        title: "Manage Users",
        description: "Manage admin users and their roles",
        icon: <Users className="w-6 h-6 text-green-600" />,
        href: "/admin/users",
        color: "bg-green-100"
      });
    }

    return actions;
  };

  return (
    <AdminLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-1">
          Welcome back, {adminUser?.full_name || adminUser?.username || 'Admin'}
        </p>
        <div className="flex flex-wrap gap-1 mt-2">
          {adminUser?.roles.map(role => (
            <span
              key={role.name}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {role.name.replace('_', ' ').toUpperCase()}
            </span>
          ))}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Trips"
          value={stats.totalTrips}
          icon={<Map className="w-6 h-6 text-blue-600" />}
          color="border-blue-600"
          loading={loading}
        />
        <StatCard
          title="Active Trips"
          value={stats.activeTrips}
          icon={<CheckCircle className="w-6 h-6 text-green-600" />}
          color="border-green-600"
          loading={loading}
        />
        <StatCard
          title="Total Blogs"
          value={stats.totalBlogs}
          icon={<FileText className="w-6 h-6 text-purple-600" />}
          color="border-purple-600"
          loading={loading}
        />
        <StatCard
          title="New Inquiries"
          value={stats.newInquiries}
          icon={<MessageSquare className="w-6 h-6 text-amber-600" />}
          color="border-amber-600"
          loading={loading}
        />
      </div>

      {/* Quick Actions */}
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {getQuickActions().map((action, index) => (
          <QuickAction
            key={index}
            title={action.title}
            description={action.description}
            icon={action.icon}
            href={action.href}
            color={action.color}
          />
        ))}
      </div>

      {getQuickActions().length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
          <p className="text-sm">No quick actions available.</p>
          <p className="text-xs mt-1">Contact your administrator for access.</p>
        </div>
      )}

      {/* Recent Activity Section could be added here */}
    </AdminLayout>
  );
} 