'use client';

import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Star,
  Heart,
  Share2,
  Mountain,
  CheckCircle,
  XCircle,
  Info,
  Shield,
  Package,
  Activity,
  Train,
  Home,
  Phone,
  CreditCard,
  AlertTriangle,
  Backpack,
  ImageIcon,
  FileText,
  DollarSign,
  Tag,
  Edit3,
  Plus,
  Trash2,
  Save,
  Upload
} from 'lucide-react';
import { TripFormData, TripItinerary } from '@/types/trip';

// Custom types for the form
interface CancellationPolicy {
  days_before: number;
  refund_percentage: number;
  description?: string;
  name?: string;
  value?: string;
}

interface TripFormProps {
  initialData?: Partial<TripFormData>;
  onSubmit: (data: TripFormData) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

// Editable Text Component
const EditableText = ({ 
  value, 
  onChange, 
  className = "", 
  multiline = false, 
  placeholder = "Enter text..." 
}: {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  multiline?: boolean;
  placeholder?: string;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempValue, setTempValue] = useState(value);

  const handleSave = () => {
    onChange(tempValue);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTempValue(value);
    setIsEditing(false);
  };

  if (isEditing) {
    return (
      <div className="relative">
        {multiline ? (
          <textarea
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            className={`${className} border-2 border-blue-500 rounded-lg p-2 w-full resize-none`}
            placeholder={placeholder}
            rows={multiline ? 4 : 1}
            onBlur={handleSave}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !multiline) {
                handleSave();
              } else if (e.key === 'Escape') {
                handleCancel();
              }
            }}
            autoFocus
          />
        ) : (
          <input
            type="text"
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            className={`${className} border-2 border-blue-500 rounded-lg p-2 w-full`}
            placeholder={placeholder}
            onBlur={handleSave}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSave();
              } else if (e.key === 'Escape') {
                handleCancel();
              }
            }}
            autoFocus
          />
        )}
      </div>
    );
  }

  return (
    <div 
      className={`${className} cursor-pointer hover:bg-blue-50 rounded-lg p-2 group relative`}
      onClick={() => setIsEditing(true)}
    >
      {value || <span className="text-gray-400">{placeholder}</span>}
      <Edit3 className="w-4 h-4 text-blue-500 opacity-0 group-hover:opacity-100 absolute top-2 right-2" />
    </div>
  );
};

// Editable List Component
const EditableList = ({ 
  items, 
  onChange, 
  placeholder = "Add item..." 
}: {
  items: string[] | null;
  onChange: (items: string[]) => void;
  placeholder?: string;
}) => {
  const [newItem, setNewItem] = useState('');
  const itemList = items || [];

  const addItem = () => {
    if (newItem.trim()) {
      onChange([...itemList, newItem.trim()]);
      setNewItem('');
    }
  };

  const removeItem = (index: number) => {
    onChange(itemList.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, value: string) => {
    const updated = [...itemList];
    updated[index] = value;
    onChange(updated);
  };

  return (
    <div className="space-y-3">
      {itemList.map((item, index) => (
        <div key={index} className="flex items-start gap-3 group">
          <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0 mt-2" />
          <EditableText
            value={item}
            onChange={(value) => updateItem(index, value)}
            className="flex-1"
            placeholder="Enter item..."
          />
          <button
            onClick={() => removeItem(index)}
            className="opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 mt-2"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      ))}
      <div className="flex items-center gap-3">
        <Plus className="w-4 h-4 text-blue-600 flex-shrink-0" />
        <input
          type="text"
          value={newItem}
          onChange={(e) => setNewItem(e.target.value)}
          placeholder={placeholder}
          className="flex-1 border border-gray-300 rounded-lg p-2"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              addItem();
            }
          }}
        />
        <button
          onClick={addItem}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Add
        </button>
      </div>
    </div>
  );
};

export default function TripForm({ initialData, onSubmit, onCancel, isLoading }: TripFormProps) {
  const [formData, setFormData] = useState<TripFormData>({
    title: '',
    description: null,
    detailed_description: null,
    destination: '',
    days: 1,
    nights: 0,
    min_age: null,
    max_age: null,
    price_per_person: 0,
    difficulty: 'easy',
    inclusions: null,
    exclusions: null,
    itinerary: null,
    featured_image_url: null,
    is_trek: false,
    is_active: true,
    is_featured: false,
    available_from: null,
    available_to: null,
    category: null,
    mode_of_travel: null,
    pickup_location: null,
    drop_location: null,
    property_used: null,
    activities: null,
    optional_activities: null,
    benefits: null,
    safety_supervision: null,
    things_to_carry: null,
    available_dates: null,
    commercial_price: null,
    payment_terms: null,
    cancellation_policy: null,
    special_notes: null,
    ...initialData
  });

  // Helper function to ensure itinerary is an array
  const getItineraryArray = useCallback(() => {
    if (!formData.itinerary) return [];
    
    // If itinerary is already an array, return it
    if (Array.isArray(formData.itinerary)) {
      return formData.itinerary;
    }
    
    // If itinerary is an object, convert it to array
    if (typeof formData.itinerary === 'object') {
      return Object.entries(formData.itinerary).map(([day, details]: [string, any]) => ({
        day: parseInt(day),
        title: details.title || `Day ${day}`,
        description: details.description || '',
        activities: details.activities || [],
        meals: details.meals || { breakfast: false, lunch: false, dinner: false },
        accommodation: details.accommodation || ''
      }));
    }
    
    return [];
  }, [formData.itinerary]);

  // Helper function to ensure cancellation_policy is an array
  const getCancellationPolicyArray = useCallback(() => {
    if (!formData.cancellation_policy) return [];
    
    // If cancellation_policy is already an array, return it
    if (Array.isArray(formData.cancellation_policy)) {
      return formData.cancellation_policy;
    }
    
    // If cancellation_policy is an object (from database), convert it to array
    if (typeof formData.cancellation_policy === 'object') {
      return Object.entries(formData.cancellation_policy).map(([key, value]) => ({
        name: key.replace(/_/g, ' '),  // Convert keys like "last_7_days" to "last 7 days"
        value: value as string,
        days_before: 0,  // Default values for our form structure
        refund_percentage: 0,
        description: value as string
      }));
    }
    
    // If cancellation_policy is a string (shouldn't happen, but just in case)
    if (typeof formData.cancellation_policy === 'string') {
      return [{
        name: 'policy',
        value: formData.cancellation_policy,
        days_before: 0,
        refund_percentage: 0,
        description: formData.cancellation_policy
      }];
    }
    
    return [];
  }, [formData.cancellation_policy]);

  const updateField = useCallback((field: keyof TripFormData, value: any) => {
    setFormData(prev => {
      // Special handling for days field to sync with itinerary
      if (field === 'days') {
        const currentItinerary = getItineraryArray();
        const currentDays = prev.days;
        const newDays = value;
        
        // If days are increased, add new itinerary days
        if (newDays > currentDays) {
          const daysToAdd = newDays - currentDays;
          const newItineraryDays = Array(daysToAdd).fill(0).map((_, index) => {
            const dayNumber = currentDays + index + 1;
            return {
              day: dayNumber,
              title: `Day ${dayNumber}`,
              description: '',
              activities: [],
              meals: { breakfast: false, lunch: false, dinner: false },
              accommodation: ''
            };
          });
          
          return { 
            ...prev, 
            [field]: value,
            itinerary: [...currentItinerary, ...newItineraryDays]
          };
        }
        
        // If days are decreased, remove itinerary days
        if (newDays < currentDays) {
          return { 
            ...prev, 
            [field]: value,
            itinerary: currentItinerary.slice(0, newDays)
          };
        }
      }
      
      return { ...prev, [field]: value };
    });
  }, [getItineraryArray]);

  // Function to handle adding a new itinerary day manually
  const addItineraryDay = useCallback(() => {
    const currentItinerary = getItineraryArray();
    const nextDayNumber = currentItinerary.length + 1;
    
    const newDay = {
      day: nextDayNumber,
      title: `Day ${nextDayNumber}`,
      description: '',
      activities: [],
      meals: { breakfast: false, lunch: false, dinner: false },
      accommodation: ''
    };
    
    // Update both itinerary and days count
    setFormData(prev => ({
      ...prev,
      days: prev.days + 1,
      itinerary: [...currentItinerary, newDay]
    }));
  }, [getItineraryArray]);

  // Function to handle removing an itinerary day
  const removeItineraryDay = useCallback((index: number) => {
    const currentItinerary = getItineraryArray();
    const newItinerary = [...currentItinerary];
    newItinerary.splice(index, 1);
    
    // Renumber the days after deletion
    const renumberedItinerary = newItinerary.map((item, idx) => ({
      ...item,
      day: idx + 1,
      title: item.title === `Day ${item.day}` ? `Day ${idx + 1}` : item.title
    }));
    
    // Update both itinerary and days count
    setFormData(prev => ({
      ...prev,
      days: prev.days - 1,
      itinerary: renumberedItinerary
    }));
  }, [getItineraryArray]);

  const handleSubmit = () => {
    // Create a copy of the form data to submit
    const dataToSubmit = { ...formData } as any;
    
    // Handle itinerary
    if (!dataToSubmit.itinerary || !Array.isArray(dataToSubmit.itinerary)) {
      dataToSubmit.itinerary = getItineraryArray();
    }
    
    // Ensure itinerary items have all required fields
    if (dataToSubmit.itinerary) {
      dataToSubmit.itinerary = dataToSubmit.itinerary.map((day: TripItinerary) => ({
        ...day,
        activities: day.activities || [],
        meals: day.meals || { breakfast: false, lunch: false, dinner: false },
      }));
    }
    
    // Handle cancellation_policy - convert from array to object for database storage
    if (dataToSubmit.cancellation_policy && Array.isArray(dataToSubmit.cancellation_policy)) {
      const policyObject: Record<string, string> = {};
      
      dataToSubmit.cancellation_policy.forEach((policy: CancellationPolicy) => {
        if (policy.name && policy.value) {
          // Convert name to snake_case for database keys
          const key = policy.name.trim().toLowerCase().replace(/\s+/g, '_');
          policyObject[key] = policy.value.trim();
        }
      });
      
      // Only set if we have policies
      if (Object.keys(policyObject).length > 0) {
        dataToSubmit.cancellation_policy = policyObject;
      } else {
        dataToSubmit.cancellation_policy = null;
      }
    }
    
    // Handle other simple array fields
    const ensureArray = (value: any) => Array.isArray(value) ? value : [];
    
    dataToSubmit.inclusions = ensureArray(dataToSubmit.inclusions);
    dataToSubmit.exclusions = ensureArray(dataToSubmit.exclusions);
    dataToSubmit.activities = ensureArray(dataToSubmit.activities);
    dataToSubmit.optional_activities = ensureArray(dataToSubmit.optional_activities);
    dataToSubmit.benefits = ensureArray(dataToSubmit.benefits);
    dataToSubmit.safety_supervision = ensureArray(dataToSubmit.safety_supervision);
    dataToSubmit.things_to_carry = ensureArray(dataToSubmit.things_to_carry);
    dataToSubmit.available_dates = ensureArray(dataToSubmit.available_dates);
    dataToSubmit.special_notes = ensureArray(dataToSubmit.special_notes);
    
    // Ensure required fields have values
    dataToSubmit.min_age = dataToSubmit.min_age || null;
    dataToSubmit.max_age = dataToSubmit.max_age || null;
    dataToSubmit.commercial_price = dataToSubmit.commercial_price || null;
    
    onSubmit(dataToSubmit as TripFormData);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Fixed Action Bar */}
      <div className="fixed top-0 left-0 right-0 bg-white shadow-lg z-50 border-b">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">
            {initialData ? 'Edit Trip' : 'Create New Trip'}
          </h1>
          <div className="flex gap-4">
            {onCancel && (
              <button
                onClick={onCancel}
                className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                disabled={isLoading}
              >
                Cancel
              </button>
            )}
            <button
              onClick={handleSubmit}
              disabled={isLoading}
              className="px-6 py-2 bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-lg hover:from-blue-700 hover:to-green-700 flex items-center gap-2 disabled:opacity-50"
            >
              <Save className="w-4 h-4" />
              {isLoading ? 'Saving...' : 'Save Trip'}
            </button>
          </div>
        </div>
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="pt-20"
      >
        {/* Hero Section */}
        <motion.section variants={itemVariants} className="relative h-[70vh] overflow-hidden">
          <div className="relative w-full h-full">
            <Image
              src={formData.featured_image_url || '/images/fallback-trip.jpg'}
              alt={formData.title || 'Trip Image'}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
            
            {/* Image Upload Button */}
            <div className="absolute top-6 right-6 z-10">
              <button
                onClick={() => {
                  const newUrl = prompt('Enter image URL:', formData.featured_image_url || '');
                  if (newUrl !== null) {
                    updateField('featured_image_url', newUrl);
                  }
                }}
                className="bg-white/20 backdrop-blur-sm hover:bg-white/30 px-4 py-2 rounded-lg text-white flex items-center gap-2"
              >
                <Upload className="w-4 h-4" />
                Change Image
              </button>
            </div>

            {/* Hero Content */}
            <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
              <div className="max-w-7xl mx-auto">
                <div className="flex flex-wrap items-end justify-between gap-6">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="px-3 py-1 bg-blue-600 rounded-full">
                        <span className="text-sm font-medium text-white bg-transparent border-none p-0">
                          {formData.destination}
                        </span>
                      </div>
                    </div>
                    
                    <EditableText
                      value={formData.title}
                      onChange={(value) => updateField('title', value)}
                      className="text-4xl md:text-6xl font-bold mb-2 text-white bg-transparent border-none p-0"
                      placeholder="Trip Title"
                    />
                    
                    <div className="flex flex-wrap items-center gap-6 text-white/80 mt-4">
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        <span>{formData.days}</span>
                        <span>Days</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        <span>{formData.nights}</span>
                        <span>Nights</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        <span>Educational Tour</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mountain className="w-5 h-5" />
                        <span className="capitalize">{formData.difficulty}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-3xl font-bold mb-2">
                      ₹{formData.price_per_person.toLocaleString()}
                    </div>
                    <div className="text-white/80 mb-4">per person</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.section>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Left Column - Main Content */}
            <div className="lg:col-span-2 space-y-12">
              {/* Trip Overview */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Trip Overview</h2>
                <EditableText
                  value={formData.description || ''}
                  onChange={(value) => updateField('description', value)}
                  className="text-gray-700 text-lg leading-relaxed mb-8"
                  multiline
                  placeholder="Enter trip description..."
                />

                {/* Quick Info Grid */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Clock className="w-5 h-5 text-blue-600" />
                      <div>
                        <div className="font-medium text-gray-900">Duration</div>
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            value={formData.days}
                            onChange={(e) => updateField('days', parseInt(e.target.value) || 1)}
                            className="w-16 border border-gray-300 rounded px-2 py-1"
                            min="1"
                          />
                          <span>Days,</span>
                          <input
                            type="number"
                            value={formData.nights}
                            onChange={(e) => updateField('nights', parseInt(e.target.value) || 0)}
                            className="w-16 border border-gray-300 rounded px-2 py-1"
                            min="0"
                          />
                          <span>Nights</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Users className="w-5 h-5 text-blue-600" />
                      <div>
                        <div className="font-medium text-gray-900">Age Range</div>
                        <div className="flex items-center gap-2 text-gray-600">
                          <input
                            type="number"
                            value={formData.min_age || ''}
                            onChange={(e) => updateField('min_age', parseInt(e.target.value) || null)}
                            className="w-16 border border-gray-300 rounded px-2 py-1"
                            min="1"
                            placeholder="Min"
                          />
                          -
                          <input
                            type="number"
                            value={formData.max_age || ''}
                            onChange={(e) => updateField('max_age', parseInt(e.target.value) || null)}
                            className="w-16 border border-gray-300 rounded px-2 py-1"
                            min="1"
                            placeholder="Max"
                          />
                          <span>years</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Mountain className="w-5 h-5 text-blue-600" />
                      <div>
                        <div className="font-medium text-gray-900">Difficulty</div>
                        <div>
                          <select
                            value={formData.difficulty}
                            onChange={(e) => updateField('difficulty', e.target.value as any)}
                            className="border border-gray-300 rounded px-2 py-1 capitalize"
                          >
                            <option value="easy">Easy</option>
                            <option value="moderate">Moderate</option>
                            <option value="challenging">Challenging</option>
                            <option value="extreme">Extreme</option>
                          </select>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <MapPin className="w-5 h-5 text-blue-600" />
                      <div>
                        <div className="font-medium text-gray-900">Destination</div>
                        <div>
                          <input
                            type="text"
                            value={formData.destination}
                            onChange={(e) => updateField('destination', e.target.value)}
                            className="border border-gray-300 rounded px-2 py-1 w-full"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Trip Details */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">About This Trip</h2>
                <EditableText
                  value={formData.detailed_description || ''}
                  onChange={(value) => updateField('detailed_description', value)}
                  className="text-gray-700 text-lg leading-relaxed"
                  multiline
                  placeholder="Enter detailed description..."
                />
              </motion.section>

              {/* Itinerary */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Trip Itinerary</h2>
                <div className="space-y-6">
                  {(getItineraryArray() || []).map((day, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 relative">
                      <button
                        onClick={() => removeItineraryDay(index)}
                        className="absolute top-2 right-2 text-red-500"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Day
                          </label>
                          <input
                            type="number"
                            value={index + 1} // Auto-number based on index
                            readOnly
                            className="w-full border border-gray-200 rounded p-2 bg-gray-50"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Title
                          </label>
                          <input
                            type="text"
                            value={day.title}
                            onChange={(e) => {
                              const newItinerary = [...getItineraryArray()];
                              newItinerary[index] = {
                                ...newItinerary[index],
                                title: e.target.value,
                                day: index + 1 // Ensure day number matches index+1
                              };
                              updateField('itinerary', newItinerary);
                            }}
                            className="w-full border border-gray-300 rounded p-2"
                          />
                        </div>
                      </div>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <textarea
                          value={day.description}
                          onChange={(e) => {
                            const newItinerary = [...getItineraryArray()];
                            newItinerary[index] = {
                              ...newItinerary[index],
                              description: e.target.value
                            };
                            updateField('itinerary', newItinerary);
                          }}
                          rows={3}
                          className="w-full border border-gray-300 rounded p-2"
                        />
                      </div>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Accommodation
                        </label>
                        <input
                          type="text"
                          value={day.accommodation || ''}
                          onChange={(e) => {
                            const newItinerary = [...getItineraryArray()];
                            newItinerary[index] = {
                              ...newItinerary[index],
                              accommodation: e.target.value
                            };
                            updateField('itinerary', newItinerary);
                          }}
                          className="w-full border border-gray-300 rounded p-2"
                        />
                      </div>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Activities
                        </label>
                        <div className="flex flex-wrap gap-2">
                          {(day.activities || []).map((activity: string, actIndex: number) => (
                            <div key={actIndex} className="flex items-center bg-blue-50 rounded-full px-3 py-1">
                              <span>{activity}</span>
                              <button
                                onClick={() => {
                                  const newItinerary = [...getItineraryArray()];
                                  const newActivities = [...(newItinerary[index].activities || [])];
                                  newActivities.splice(actIndex, 1);
                                  newItinerary[index] = {
                                    ...newItinerary[index],
                                    activities: newActivities
                                  };
                                  updateField('itinerary', newItinerary);
                                }}
                                className="ml-2 text-red-500"
                              >
                                <XCircle className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <form
                            onSubmit={(e) => {
                              e.preventDefault();
                              const input = e.currentTarget.elements.namedItem('newActivity') as HTMLInputElement;
                              if (input.value.trim()) {
                                const newItinerary = [...getItineraryArray()];
                                const newActivities = [...(newItinerary[index].activities || []), input.value.trim()];
                                newItinerary[index] = {
                                  ...newItinerary[index],
                                  activities: newActivities
                                };
                                updateField('itinerary', newItinerary);
                                input.value = '';
                              }
                            }}
                            className="flex"
                          >
                            <input
                              type="text"
                              name="newActivity"
                              placeholder="Add activity..."
                              className="border border-gray-300 rounded-l p-1 text-sm"
                            />
                            <button
                              type="submit"
                              className="bg-blue-500 text-white rounded-r p-1 text-sm"
                            >
                              <Plus className="w-4 h-4" />
                            </button>
                          </form>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Meals
                        </label>
                        <div className="flex gap-4">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={day.meals?.breakfast || false}
                              onChange={(e) => {
                                const newItinerary = [...getItineraryArray()];
                                newItinerary[index] = {
                                  ...newItinerary[index],
                                  meals: {
                                    ...(newItinerary[index].meals || {}),
                                    breakfast: e.target.checked
                                  }
                                };
                                updateField('itinerary', newItinerary);
                              }}
                              className="mr-2"
                            />
                            Breakfast
                          </label>
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={day.meals?.lunch || false}
                              onChange={(e) => {
                                const newItinerary = [...getItineraryArray()];
                                newItinerary[index] = {
                                  ...newItinerary[index],
                                  meals: {
                                    ...(newItinerary[index].meals || {}),
                                    lunch: e.target.checked
                                  }
                                };
                                updateField('itinerary', newItinerary);
                              }}
                              className="mr-2"
                            />
                            Lunch
                          </label>
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={day.meals?.dinner || false}
                              onChange={(e) => {
                                const newItinerary = [...getItineraryArray()];
                                newItinerary[index] = {
                                  ...newItinerary[index],
                                  meals: {
                                    ...(newItinerary[index].meals || {}),
                                    dinner: e.target.checked
                                  }
                                };
                                updateField('itinerary', newItinerary);
                              }}
                              className="mr-2"
                            />
                            Dinner
                          </label>
                        </div>
                      </div>
                    </div>
                  ))}
                  <button
                    onClick={addItineraryDay}
                    className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:border-gray-400"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Add Itinerary Day
                  </button>
                </div>
              </motion.section>

              {/* Travel Information */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Travel Information</h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="flex items-start gap-3">
                    <Train className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Mode of Travel</div>
                      <EditableText
                        value={formData.mode_of_travel || ''}
                        onChange={(value) => updateField('mode_of_travel', value)}
                        className="text-gray-600"
                        placeholder="Enter mode of travel..."
                      />
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Pickup Location</div>
                      <EditableText
                        value={formData.pickup_location || ''}
                        onChange={(value) => updateField('pickup_location', value)}
                        className="text-gray-600"
                        placeholder="Enter pickup location..."
                      />
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-red-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Drop Location</div>
                      <EditableText
                        value={formData.drop_location || ''}
                        onChange={(value) => updateField('drop_location', value)}
                        className="text-gray-600"
                        placeholder="Enter drop location..."
                      />
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Home className="w-5 h-5 text-purple-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Accommodation</div>
                      <EditableText
                        value={formData.property_used || ''}
                        onChange={(value) => updateField('property_used', value)}
                        className="text-gray-600"
                        placeholder="Enter accommodation details..."
                      />
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Activities */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Activities</h2>
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-4 text-gray-900">Activities Included</h3>
                  <EditableList
                    items={formData.activities}
                    onChange={(items) => updateField('activities', items)}
                    placeholder="Add activity..."
                  />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-gray-900">Optional Activities</h3>
                  <EditableList
                    items={formData.optional_activities}
                    onChange={(items) => updateField('optional_activities', items)}
                    placeholder="Add optional activity..."
                  />
                </div>
              </motion.section>

              {/* Inclusions & Exclusions */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">What's Included & Excluded</h2>
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Inclusions */}
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                      <CheckCircle className="w-5 h-5 flex-shrink-0" />
                      Included
                    </h3>
                    <EditableList
                      items={formData.inclusions}
                      onChange={(items) => updateField('inclusions', items)}
                      placeholder="Add inclusion..."
                    />
                  </div>

                  {/* Exclusions */}
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-red-700 flex items-center gap-2">
                      <XCircle className="w-5 h-5 flex-shrink-0" />
                      Not Included
                    </h3>
                    <EditableList
                      items={formData.exclusions}
                      onChange={(items) => updateField('exclusions', items)}
                      placeholder="Add exclusion..."
                    />
                  </div>
                </div>
              </motion.section>

              {/* Safety & Benefits */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Safety & Benefits</h2>
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Benefits */}
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-blue-700 flex items-center gap-2">
                      <Package className="w-5 h-5 flex-shrink-0" />
                      Trip Benefits
                    </h3>
                    <EditableList
                      items={formData.benefits}
                      onChange={(items) => updateField('benefits', items)}
                      placeholder="Add benefit..."
                    />
                  </div>

                  {/* Safety */}
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                      <Shield className="w-5 h-5 flex-shrink-0" />
                      Safety & Supervision
                    </h3>
                    <EditableList
                      items={formData.safety_supervision}
                      onChange={(items) => updateField('safety_supervision', items)}
                      placeholder="Add safety measure..."
                    />
                  </div>
                </div>
              </motion.section>

              {/* Things to Carry */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900 flex items-center gap-3">
                  <Backpack className="w-8 h-8 text-orange-600 flex-shrink-0" />
                  Things to Carry
                </h2>
                <EditableList
                  items={formData.things_to_carry}
                  onChange={(items) => updateField('things_to_carry', items)}
                  placeholder="Add item to carry..."
                />
              </motion.section>

              {/* Special Notes */}
              <motion.section variants={itemVariants} className="bg-yellow-50 border border-yellow-200 rounded-2xl p-8">
                <h2 className="text-2xl font-bold mb-6 text-yellow-800 flex items-center gap-3">
                  <Info className="w-6 h-6 flex-shrink-0" />
                  Important Notes
                </h2>
                <EditableList
                  items={formData.special_notes}
                  onChange={(items) => updateField('special_notes', items)}
                  placeholder="Add important note..."
                />
              </motion.section>

              {/* Payment Terms */}
              <motion.section variants={itemVariants} className="bg-blue-50 border border-blue-200 rounded-2xl p-8">
                <h2 className="text-2xl font-bold mb-6 text-blue-800 flex items-center gap-3">
                  <DollarSign className="w-6 h-6" />
                  Payment Terms
                </h2>
                <EditableText
                  value={formData.payment_terms || ''}
                  onChange={(value) => updateField('payment_terms', value)}
                  className="text-blue-800 leading-relaxed"
                  multiline
                  placeholder="Enter payment terms..."
                />
              </motion.section>
            </div>

            {/* Right Column - Settings Sidebar */}
            <div className="lg:col-span-1">
              <motion.div variants={itemVariants} className="sticky top-24 space-y-6">
                {/* Pricing Card */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <h3 className="text-xl font-semibold mb-4">Pricing & Settings</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Price per Person (₹)
                      </label>
                      <input
                        type="number"
                        value={formData.price_per_person}
                        onChange={(e) => updateField('price_per_person', parseInt(e.target.value) || 0)}
                        className="w-full border border-gray-300 rounded-lg p-2"
                        min="0"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Commercial Price (₹)
                      </label>
                      <input
                        type="number"
                        value={formData.commercial_price || ''}
                        onChange={(e) => updateField('commercial_price', parseInt(e.target.value) || null)}
                        className="w-full border border-gray-300 rounded-lg p-2"
                        min="0"
                        placeholder="Optional"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Category
                      </label>
                      <input
                        type="text"
                        value={formData.category || ''}
                        onChange={(e) => updateField('category', e.target.value)}
                        className="w-full border border-gray-300 rounded-lg p-2"
                        placeholder="e.g., Adventure, Cultural"
                      />
                    </div>

                    <div className="space-y-3">
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={formData.is_trek === true}
                          onChange={(e) => updateField('is_trek', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-gray-700">Is Trek</span>
                      </label>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Available From
                      </label>
                      <input
                        type="date"
                        value={formData.available_from || ''}
                        onChange={(e) => updateField('available_from', e.target.value)}
                        className="w-full border border-gray-300 rounded-lg p-2"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Available To
                      </label>
                      <input
                        type="date"
                        value={formData.available_to || ''}
                        onChange={(e) => updateField('available_to', e.target.value)}
                        className="w-full border border-gray-300 rounded-lg p-2"
                      />
                    </div>
                  </div>
                </div>

                {/* Available Dates */}
                <div className="bg-white rounded-2xl p-6 shadow-lg mt-6">
                  <h3 className="text-xl font-semibold mb-4">Available Dates</h3>
                  <EditableList
                    items={formData.available_dates}
                    onChange={(items) => updateField('available_dates', items)}
                    placeholder="Add date (e.g., June 15-20, 2023)..."
                  />
                </div>

                {/* Cancellation Policy */}
                <div className="bg-white rounded-2xl p-6 shadow-lg mt-6">
                  <h3 className="text-xl font-semibold mb-4">Cancellation Policy</h3>
                  <div className="space-y-4">
                    {(getCancellationPolicyArray() || []).map((policy, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4 relative">
                        <button
                          onClick={() => {
                            const policyArray = [...getCancellationPolicyArray()];
                            policyArray.splice(index, 1);
                            updateField('cancellation_policy', policyArray);
                          }}
                          className="absolute top-2 right-2 text-red-500"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Policy Name
                            </label>
                            <input
                              type="text"
                              value={policy.name || ''}
                              onChange={(e) => {
                                const policyArray = [...getCancellationPolicyArray()];
                                policyArray[index] = {
                                  ...policyArray[index],
                                  name: e.target.value
                                };
                                updateField('cancellation_policy', policyArray);
                              }}
                              className="w-full border border-gray-300 rounded p-2"
                              placeholder="e.g., Minimum, Last 7 days"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Refund Value
                            </label>
                            <input
                              type="text"
                              value={policy.value || ''}
                              onChange={(e) => {
                                const policyArray = [...getCancellationPolicyArray()];
                                policyArray[index] = {
                                  ...policyArray[index],
                                  value: e.target.value
                                };
                                updateField('cancellation_policy', policyArray);
                              }}
                              className="w-full border border-gray-300 rounded p-2"
                              placeholder="e.g., 25%, No Refund"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                    <button
                      onClick={() => {
                        const newPolicy = { name: '', value: '', days_before: 0, refund_percentage: 0, description: '' };
                        updateField('cancellation_policy', [...getCancellationPolicyArray(), newPolicy]);
                      }}
                      className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:border-gray-400"
                    >
                      <Plus className="w-5 h-5 mr-2" />
                      Add Cancellation Policy
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}