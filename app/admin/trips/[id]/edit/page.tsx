'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import TripForm from '../../components/Trip-FormCompleted';
import { Trip, TripFormData } from '@/types/trip';
import AdminLayout from '@/components/layout/AdminLayout';

export default function EditTripPage() {
  const params = useParams();
  const router = useRouter();
  const [trip, setTrip] = useState<Trip | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTrip = async () => {
      try {
        const response = await fetch(`/api/admin/trips/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch trip');
        }
        const data = await response.json();
        setTrip(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch trip');
      }
    };

    fetchTrip();
  }, [params.id]);

  const handleSubmit = async (data: TripFormData) => {
    setIsLoading(true);
    try {
      if (!trip) {
        throw new Error('Trip data is missing');
      }
      
      const response = await fetch(`/api/admin/trips/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          is_active: trip.is_active,
          is_featured: trip.is_featured
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update trip');
      }
      
      router.push('/admin/trips' as any);
    } catch (error) {
      setIsLoading(false);
      setError(error instanceof Error ? error.message : 'Failed to update trip');
    }
  };

  if (error) {
    return (
      <AdminLayout>
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!trip) {
    return (
      <AdminLayout>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-gray-600">Loading trip...</p>
        </div>
      </AdminLayout>
    );
  }

  const formData: TripFormData = {
    title: trip.title,
    description: trip.description || '',
    detailed_description: trip.detailed_description || '',
    destination: trip.destination,
    days: trip.days,
    nights: trip.nights,
    price_per_person: trip.price_per_person,
    difficulty: trip.difficulty,
    inclusions: trip.inclusions || [],
    exclusions: trip.exclusions || [],
    itinerary: trip.itinerary || [],
    featured_image_url: trip.featured_image_url || '',
    is_active: trip.is_active,
    is_featured: trip.is_featured,
    is_trek: trip.is_trek || false,
    min_age: trip.min_age || null,
    max_age: trip.max_age || null,
    available_from: trip.available_from,
    available_to: trip.available_to,
    category: trip.category || '',
    mode_of_travel: trip.mode_of_travel || '',
    pickup_location: trip.pickup_location || '',
    drop_location: trip.drop_location || '',
    property_used: trip.property_used || '',
    activities: trip.activities || [],
    optional_activities: trip.optional_activities || [],
    benefits: trip.benefits || [],
    safety_supervision: trip.safety_supervision || [],
    things_to_carry: trip.things_to_carry || [],
    available_dates: trip.available_dates || [],
    commercial_price: trip.commercial_price,
    payment_terms: trip.payment_terms || '',
    cancellation_policy: trip.cancellation_policy,
    special_notes: trip.special_notes || [],
  };

  return (
    <AdminLayout>
      <div className="mb-8">
        <Link href="/admin/trips" className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center w-fit mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Trips
        </Link>
      </div>
      <TripForm
        initialData={formData}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        onCancel={() => router.back()}
      />
    </AdminLayout>
  );
} 