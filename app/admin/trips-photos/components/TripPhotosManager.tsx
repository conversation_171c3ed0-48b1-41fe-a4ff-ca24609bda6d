'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { 
  Upload, 
  X, 
  Check, 
  Loader2, 
  Plus, 
  ChevronDown, 
  ChevronUp, 
  Edit, 
  Trash2,
  ExternalLink,
  Search,
  Lock,
  Image as ImageIcon
} from 'lucide-react';
import { TripPhotoDetails, TripPhotoDetailsFormData } from '@/types/trip-photos';
import TripPhotoDetailsForm from './TripPhotoDetailsForm';
import TripPhotoUploader from './TripPhotoUploader';

export default function TripPhotosManager() {
  const [tripPhotoDetails, setTripPhotoDetails] = useState<TripPhotoDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState('');
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<TripPhotoDetails | null>(null);
  
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  // Fetch trip photo details
  const fetchTripPhotoDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (search) queryParams.set('search', search);

      const response = await fetch(`/api/admin/trips-photos?${queryParams}`);
      if (!response.ok) {
        throw new Error('Failed to fetch trip photo details');
      }

      const data = await response.json();
      setTripPhotoDetails(data.data);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTripPhotoDetails();
  }, [pagination.page, search]);

  const handleCreateNew = () => {
    setEditingItem(null);
    setIsFormOpen(true);
  };

  const handleEdit = (item: TripPhotoDetails) => {
    setEditingItem(item);
    setIsFormOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this trip photo album?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/trips-photos/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete trip photo album');
      }

      // Refresh the list
      fetchTripPhotoDetails();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete trip photo album');
    }
  };

  const handleFormSubmit = async (data: TripPhotoDetailsFormData) => {
    try {
      const url = editingItem 
        ? `/api/admin/trips-photos/${editingItem.id}` 
        : '/api/admin/trips-photos';
      
      const method = editingItem ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${editingItem ? 'update' : 'create'} trip photo album`);
      }

      setIsFormOpen(false);
      fetchTripPhotoDetails();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const toggleExpand = (id: string) => {
    setExpandedId(expandedId === id ? null : id);
  };

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Header and Actions */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Trip Photo Albums</h1>
        <button
          onClick={handleCreateNew}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create New Album
        </button>
      </div>

      {/* Search */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          placeholder="Search trip photo albums..."
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
        />
      </div>

      {/* Form Modal */}
      {isFormOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-800">
                  {editingItem ? 'Edit Trip Photo Album' : 'Create New Trip Photo Album'}
                </h2>
                <button onClick={() => setIsFormOpen(false)} className="text-gray-500 hover:text-gray-700">
                  <X className="h-5 w-5" />
                </button>
              </div>
              <TripPhotoDetailsForm 
                initialData={editingItem || undefined} 
                onSubmit={handleFormSubmit} 
                onCancel={() => setIsFormOpen(false)}
              />
            </div>
          </div>
        </div>
      )}

      {/* Trip Photo Albums List */}
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 text-primary-600 animate-spin" />
        </div>
      ) : tripPhotoDetails.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <p className="text-gray-500">No trip photo albums found</p>
          <button
            onClick={handleCreateNew}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Your First Album
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {tripPhotoDetails.map((item) => (
            <div key={item.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div 
                className="p-4 cursor-pointer hover:bg-gray-50 flex justify-between items-center"
                onClick={() => toggleExpand(item.id)}
              >
                <div className="flex items-center">
                  {item.featured_image_url ? (
                    <div className="w-12 h-12 mr-4 relative rounded-md overflow-hidden">
                      <Image 
                        src={item.featured_image_url} 
                        alt={item.trip_name} 
                        fill 
                        className="object-cover" 
                      />
                    </div>
                  ) : (
                    <div className="w-12 h-12 mr-4 bg-gray-200 rounded-md flex items-center justify-center">
                      <ImageIcon className="h-6 w-6 text-gray-400" />
                    </div>
                  )}
                  <div>
                    <h3 className="font-medium text-gray-900">{item.trip_name}</h3>
                    {item.access_password && (
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <Lock className="h-3 w-3 mr-1" />
                        Password Protected
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center">
                  <button 
                    className="p-1 text-gray-400 hover:text-gray-500 mr-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEdit(item);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button 
                    className="p-1 text-gray-400 hover:text-red-500 mr-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(item.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                  {expandedId === item.id ? (
                    <ChevronUp className="h-5 w-5 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-400" />
                  )}
                </div>
              </div>

              {/* Expanded Panel */}
              {expandedId === item.id && (
                <div className="border-t border-gray-200 p-4 bg-gray-50">
                  <div className="mb-4">
                    {item.trip_description && (
                      <p className="text-gray-600 text-sm mb-2">{item.trip_description}</p>
                    )}
                    {item.google_drive_link && (
                      <a 
                        href={item.google_drive_link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-sm text-primary-600 hover:text-primary-700"
                      >
                        <ExternalLink className="h-4 w-4 mr-1" />
                        Open Google Drive Link
                      </a>
                    )}
                  </div>
                  
                  <TripPhotoUploader tripPhotoDetails={item} />
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {tripPhotoDetails.length > 0 && (
        <div className="flex justify-between items-center mt-6">
          <button
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
            disabled={pagination.page === 1}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <span className="text-sm text-gray-700">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <button
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
            disabled={pagination.page === pagination.totalPages}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
} 