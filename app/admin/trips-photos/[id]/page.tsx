import { createServerSupabase } from '@/lib/supabase-server';
import AdminLayout from '@/components/layout/AdminLayout';
import TripPhotoDetailContent from './components/TripPhotoDetailContent';
import { TripPhotoDetails } from '@/types/trip-photos';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

interface TripPhotoDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function TripPhotoDetailPage({ params }: TripPhotoDetailPageProps) {
  const { id } = await params;
  
  // Fetch the trip photo details from server
  const supabase = createServerSupabase();
  const { data, error } = await supabase
    .from('trip_photos_details')
    .select('*')
    .eq('id', id)
    .single();
    
  // Convert to proper TripPhotoDetails type with non-nullable created_at and updated_at
  const tripPhotoDetails: TripPhotoDetails | null = data ? {
    ...data,
    created_at: data.created_at || new Date().toISOString(),
    updated_at: data.updated_at || new Date().toISOString()
  } : null;

  return (
    <AdminLayout>
      <TripPhotoDetailContent tripPhotoDetails={tripPhotoDetails} />
    </AdminLayout>
  );
} 