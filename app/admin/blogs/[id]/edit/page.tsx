'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import BlogForm from '../../components/BlogForm';
import { BlogPost, BlogFormData } from '@/types/blog';
import AdminLayout from '@/components/layout/AdminLayout';

export default function EditBlogPage() {
  const params = useParams();
  const router = useRouter();
  const [blog, setBlog] = useState<BlogPost | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        const response = await fetch(`/api/admin/blogs/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch blog post');
        }
        const data = await response.json();
        setBlog(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch blog post');
      }
    };

    fetchBlog();
  }, [params.id]);

  const handleSubmit = async (data: BlogFormData) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/blogs/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update blog post');
      }
    } catch (error) {
      setIsLoading(false);
      throw error;
    }
  };

  if (error) {
    return (
      <AdminLayout>
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
              <Link
                href="/admin/blogs"
                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 mt-2"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Blogs
              </Link>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!blog) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">Loading blog post...</p>
        </div>
      </AdminLayout>
    );
  }

  const formData: BlogFormData = {
    title: blog.title,
    content: blog.content,
    excerpt: blog.excerpt,
    featured_image_url: blog.featured_image_url,
    category: blog.category,
    tags: blog.tags,
    is_published: blog.is_published,
    seo_title: blog.seo_title,
    seo_description: blog.seo_description,
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <Link
          href="/admin/blogs"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Blogs
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Edit Blog Post</h1>
        <p className="text-gray-600 mt-1">Make your changes to the blog post and save when you're done</p>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <BlogForm
          initialData={formData}
          onSubmit={handleSubmit}
          isLoading={isLoading}
        />
      </div>
    </AdminLayout>
  );
} 