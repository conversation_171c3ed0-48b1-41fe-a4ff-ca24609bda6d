'use client';

import { useState } from 'react';
import BlogForm from '../components/BlogForm';
import { BlogFormData } from '@/types/blog';

export default function NewBlogPage() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: BlogFormData) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/blogs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create blog post');
      }
    } catch (error) {
      setIsLoading(false);
      throw error;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create New Blog Post</h1>
        </div>
        <div className="bg-white shadow rounded-lg p-6">
          <BlogForm onSubmit={handleSubmit} isLoading={isLoading} />
        </div>
      </div>
    </div>
  );
} 