'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import BlogForm from '../components/BlogForm';
import { BlogFormData } from '@/types/blog';
import AdminLayout from '@/components/layout/AdminLayout';

export default function NewBlogPage() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: BlogFormData) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/blogs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create blog post');
      }
    } catch (error) {
      setIsLoading(false);
      throw error;
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <Link
          href="/admin/blogs"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Blogs
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Create New Blog Post</h1>
        <p className="text-gray-600 mt-1">Write and publish a new blog post</p>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <BlogForm onSubmit={handleSubmit} isLoading={isLoading} />
      </div>
    </AdminLayout>
  );
}