import { NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import nodemailer from 'nodemailer';
import type { Database } from '@/types/supabase';

type TripRow = Database['public']['Tables']['trips']['Row'];

// Email configuration
const createTransporter = () => {
  const emailService = process.env.EMAIL_SERVICE || 'gmail';
  const emailUser = process.env.EMAIL_USER;
  const emailPassword = process.env.EMAIL_PASSWORD;

  if (!emailUser || !emailPassword) {
    console.log('Email credentials not configured. Emails will be logged to console only.');
    return null;
  }

  return nodemailer.createTransport({
    service: emailService,
    auth: {
      user: emailUser,
      pass: emailPassword,
    },
  });
};

async function sendTripDeactivationEmail(trips: TripRow[]): Promise<boolean> {
  const adminEmail = process.env.ADMIN_NOTIFICATION_EMAIL;

  if (!adminEmail) {
    console.log('❌ ADMIN_NOTIFICATION_EMAIL not configured, skipping email notification');
    return false;
  }

  const transporter = createTransporter();

  if (!transporter) {
    console.log('📧 Email service not configured, logging deactivated trips:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📋 TRIPS AUTOMATICALLY DEACTIVATED');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    trips.forEach(trip => {
      console.log(`🏷️  Trip: ${trip.title} (ID: ${trip.id})`);
      console.log(`📅 Available until: ${trip.available_to}`);
      console.log(`📅 Deactivated on: ${new Date().toISOString()}`);
      console.log('---');
    });
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    return false;
  }

  try {
    const tripsList = trips.map(trip => `
      <tr>
        <td style="padding: 12px; border-bottom: 1px solid #e2e8f0;">${trip.title}</td>
        <td style="padding: 12px; border-bottom: 1px solid #e2e8f0;">${trip.id}</td>
        <td style="padding: 12px; border-bottom: 1px solid #e2e8f0;">${trip.available_to}</td>
      </tr>
    `).join('');

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: adminEmail,
      subject: `${trips.length} Trip(s) Automatically Deactivated - Positive7`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Trips Deactivated - Positive7</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">Trips Automatically Deactivated</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Positive7 Educational Tours</p>
          </div>

          <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
            <div style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <h2 style="color: #495057; margin-top: 0; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">Deactivation Report</h2>
              <p>The following trips have been automatically deactivated because their availability end date has passed:</p>
              
              <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                <thead>
                  <tr style="background-color: #f8f9fa;">
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #e2e8f0;">Trip Title</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #e2e8f0;">Trip ID</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #e2e8f0;">Available Until</th>
                  </tr>
                </thead>
                <tbody>
                  ${tripsList}
                </tbody>
              </table>
              
              <div style="margin-top: 25px; padding-top: 15px; border-top: 1px solid #e9ecef;">
                <p>These trips have been set to inactive status in the system. To reactivate them, please update their availability dates in the admin panel.</p>
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}/admin/trips" style="display: inline-block; background-color: #4f46e5; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-top: 10px;">Go to Trip Management</a>
              </div>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
              <p style="color: #6c757d; font-size: 14px; margin: 0;">
                This is an automated notification from the Positive7 trip management system.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Trip deactivation notification sent successfully to ${adminEmail}`);
    return true;
  } catch (error) {
    console.error('❌ Error sending trip deactivation notification:', error);
    return false;
  }
}

export async function GET() {
  try {
    // Create Supabase client
    const supabase = createServerSupabase();
    
    // Get current date in ISO format (YYYY-MM-DD)
    const today = new Date().toISOString().split('T')[0];
    
    // Find trips that need to be deactivated
    const { data: tripsToDeactivate, error: fetchError } = await supabase
      .from('trips')
      .select('*')
      .eq('is_active', true)
      .lte('available_to', today)
      .not('available_to', 'is', null);
    
    if (fetchError) {
      console.error('Error fetching trips to deactivate:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch trips for deactivation' },
        { status: 500 }
      );
    }
    
    if (!tripsToDeactivate || tripsToDeactivate.length === 0) {
      return NextResponse.json(
        { message: 'No trips to deactivate' },
        { status: 200 }
      );
    }
    
    // Update trips to inactive
    const { error: updateError } = await supabase
      .from('trips')
      .update({ is_active: false })
      .in('id', tripsToDeactivate.map(trip => trip.id));
    
    if (updateError) {
      console.error('Error deactivating trips:', updateError);
      return NextResponse.json(
        { error: 'Failed to deactivate trips' },
        { status: 500 }
      );
    }
    
    // Send notification email
    await sendTripDeactivationEmail(tripsToDeactivate);
    
    return NextResponse.json({
      message: `Successfully deactivated ${tripsToDeactivate.length} trips`,
      deactivatedTrips: tripsToDeactivate.map(trip => ({
        id: trip.id,
        title: trip.title,
        available_to: trip.available_to
      }))
    }, { status: 200 });
  } catch (error) {
    console.error('Error in trip deactivation cron job:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 