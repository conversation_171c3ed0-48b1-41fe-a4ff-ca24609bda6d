import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminSupabaseClient } from '@/lib/auth-server';

// PUT /api/admin/users/[id] - Update user roles (super admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('users', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Super admin privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { role_names, is_active } = body;
    const resolvedParams = await params;
    const userId = resolvedParams.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminSupabaseClient();

    // Update user active status if provided
    if (typeof is_active === 'boolean') {
      const { error: updateError } = await adminSupabase
        .from('admin_profiles')
        .update({ is_active })
        .eq('id', userId);

      if (updateError) {
        console.error('Error updating user status:', updateError);
        return NextResponse.json(
          { error: 'Failed to update user status' },
          { status: 500 }
        );
      }
    }

    // Update roles if provided
    if (role_names && Array.isArray(role_names)) {
      // Remove existing roles
      const { error: deleteError } = await adminSupabase
        .from('admin_user_roles')
        .delete()
        .eq('user_id', userId);

      if (deleteError) {
        console.error('Error removing existing roles:', deleteError);
        return NextResponse.json(
          { error: 'Failed to update user roles' },
          { status: 500 }
        );
      }

      // Get new role IDs
      const { data: roles, error: rolesError } = await adminSupabase
        .from('admin_roles')
        .select('id, name')
        .in('name', role_names);

      if (rolesError || !roles) {
        console.error('Error fetching roles:', rolesError);
        return NextResponse.json(
          { error: 'Invalid roles specified' },
          { status: 400 }
        );
      }

      // Assign new roles
      if (roles.length > 0) {
        const userRoleInserts = roles.map(role => ({
          user_id: userId,
          role_id: role.id
        }));

        const { error: insertError } = await adminSupabase
          .from('admin_user_roles')
          .insert(userRoleInserts);

        if (insertError) {
          console.error('Error assigning new roles:', insertError);
          return NextResponse.json(
            { error: 'Failed to assign new roles' },
            { status: 500 }
          );
        }
      }
    }

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Update admin user error:', error);
    return NextResponse.json(
      { error: 'Failed to update admin user', details: error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users/[id] - Delete admin user (super admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('users', 'delete');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Super admin privileges required.' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const userId = resolvedParams.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Prevent deleting self
    if (userId === user.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminSupabaseClient();

    // Delete user from Supabase Auth (this will cascade delete related records)
    const { error: deleteError } = await adminSupabase.auth.admin.deleteUser(userId);

    if (deleteError) {
      console.error('Error deleting user:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete user', details: deleteError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Delete admin user error:', error);
    return NextResponse.json(
      { error: 'Failed to delete admin user', details: error.message },
      { status: 500 }
    );
  }
}
