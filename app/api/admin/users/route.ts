import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminSupabaseClient } from '@/lib/auth-server';

// GET /api/admin/users - List all admin users (super admin only)
export async function GET(request: NextRequest) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('users', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Super admin privileges required.' },
        { status: 403 }
      );
    }

    const adminSupabase = createAdminSupabaseClient();

    // Get all admin users
    const { data: adminUsers, error } = await adminSupabase
      .from('admin_profiles')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching admin users:', error);
      return NextResponse.json(
        { error: 'Failed to fetch admin users' },
        { status: 500 }
      );
    }

    // Get roles for each user
    const transformedUsers = await Promise.all(
      adminUsers.map(async (user) => {
        const { data: userRoles, error: rolesError } = await adminSupabase
          .from('admin_user_roles')
          .select(`
            admin_roles (
              id,
              name,
              description,
              permissions
            )
          `)
          .eq('user_id', user.id);

        if (rolesError) {
          console.error('Error fetching user roles:', rolesError);
          return { ...user, roles: [] };
        }

        const roles = userRoles
          .map(ur => ur.admin_roles)
          .filter(Boolean);

        return { ...user, roles };
      })
    );

    return NextResponse.json({ users: transformedUsers });

  } catch (error: any) {
    console.error('Get admin users error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch admin users', details: error.message },
      { status: 500 }
    );
  }
}

// POST /api/admin/users - Create new admin user (super admin only)
export async function POST(request: NextRequest) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('users', 'create');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Super admin privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { email, password, full_name, username, role_names = ['content_manager'] } = body;

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminSupabaseClient();

    // Create user with Supabase Auth
    const { data: authData, error: authError } = await adminSupabase.auth.admin.createUser({
      email,
      password,
      user_metadata: {
        full_name: full_name || email,
        username: username || email.split('@')[0]
      },
      email_confirm: true
    });

    if (authError) {
      console.error('Error creating user:', authError);
      return NextResponse.json(
        { error: 'Failed to create user', details: authError.message },
        { status: 400 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      );
    }

    // Get role IDs
    const { data: roles, error: rolesError } = await adminSupabase
      .from('admin_roles')
      .select('id, name')
      .in('name', role_names);

    if (rolesError || !roles || roles.length === 0) {
      console.error('Error fetching roles:', rolesError);
      await adminSupabase.auth.admin.deleteUser(authData.user.id);
      return NextResponse.json(
        { error: 'Invalid roles specified' },
        { status: 400 }
      );
    }

    // Assign roles to user
    const userRoleInserts = roles.map(role => ({
      user_id: authData.user.id,
      role_id: role.id
    }));

    const { error: userRolesError } = await adminSupabase
      .from('admin_user_roles')
      .insert(userRoleInserts);

    if (userRolesError) {
      console.error('Error assigning roles:', userRolesError);
      await adminSupabase.auth.admin.deleteUser(authData.user.id);
      return NextResponse.json(
        { error: 'Failed to assign roles to user' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      user: {
        id: authData.user.id,
        email: authData.user.email,
        full_name: full_name || email,
        username: username || email.split('@')[0],
        roles: roles.map(r => r.name)
      }
    });

  } catch (error: any) {
    console.error('Create admin user error:', error);
    return NextResponse.json(
      { error: 'Failed to create admin user', details: error.message },
      { status: 500 }
    );
  }
}
