import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { extractFolderIdFromUrl } from '@/lib/google-drive';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/trips-photos/[id] - Get a single trip photo details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Destructure id from params to avoid direct property access
    const { id } = await params;
    const supabase = createServerSupabase();

    const { data, error } = await supabase
      .from('trip_photos_details')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Trip photo details not found' }, { status: 404 });
      }
      throw error;
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An error occurred fetching trip photo details' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/trips-photos/[id] - Update a trip photo details
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createServerSupabase();
    // Destructure id from params to avoid direct property access
    const { id } = await params;
    const body = await request.json();

    // Basic validation
    if (!body.trip_name) {
      return NextResponse.json(
        { error: 'Trip name is required' },
        { status: 400 }
      );
    }

    // Extract folder ID from Google Drive link if provided
    const drive_folder_id = body.google_drive_link ? extractFolderIdFromUrl(body.google_drive_link) : null;

    // Update trip photo details
    const { data, error } = await supabase
      .from('trip_photos_details')
      .update({
        ...body,
        drive_folder_id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating trip photo details:', error);
      return NextResponse.json(
        { error: 'Failed to update trip photo details' },
        { status: 500 }
      );
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error in PUT /api/admin/trips-photos/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/trips-photos/[id] - Delete a trip photo details
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Destructure id from params to avoid direct property access
    const { id } = await params;
    const supabase = createServerSupabase();

    const { error } = await supabase
      .from('trip_photos_details')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An error occurred deleting trip photo details' },
      { status: 500 }
    );
  }
} 