import { NextRequest, NextResponse } from 'next/server';
import { uploadToGoogleDrive, validateFolderAccess } from '@/lib/google-drive';
import path from 'path';
import fs from 'fs-extra';
import { createServerSupabase } from '@/lib/supabase-server';

// DEPRECATED: This endpoint is maintained for backward compatibility
// New uploads use the automatic upload feature in the watermark endpoint
export async function POST(req: NextRequest) {
  console.log('[API] Starting Google Drive upload process');
  try {
    // Parse form data
    const formData = await req.formData();
    const tripPhotoDetailsId = formData.get('tripPhotoDetailsId') as string;
    const imageUrl = formData.get('imageUrl') as string;

    console.log(`[API] Processing upload for trip photo details ID: ${tripPhotoDetailsId}`);
    console.log(`[API] Image URL to upload: ${imageUrl}`);

    if (!imageUrl || !tripPhotoDetailsId) {
      console.error('[API] Required parameters missing', { imageUrl, tripPhotoDetailsId });
      return NextResponse.json(
        { message: 'Required parameters missing' },
        { status: 400 }
      );
    }

    // Get the trip photo details to get the Google Drive folder URL
    console.log('[API] Fetching trip photo details from database');
    const supabase = createServerSupabase();
    const { data: tripPhotoDetails, error } = await supabase
      .from('trip_photos_details')
      .select('*')
      .eq('id', tripPhotoDetailsId)
      .single();

    if (error || !tripPhotoDetails) {
      console.error('[API] Error fetching trip photo details:', error);
      return NextResponse.json(
        { message: 'Trip photo details not found' },
        { status: 404 }
      );
    }

    console.log(`[API] Found trip photo details: ${tripPhotoDetails.trip_name}`);

    // Check if a Google Drive folder link is provided
    if (!tripPhotoDetails.google_drive_link) {
      console.error('[API] No Google Drive folder link provided in trip photo details');
      return NextResponse.json(
        { message: 'No Google Drive folder link provided for this trip' },
        { status: 400 }
      );
    }

    // Check if we have a folder ID
    if (!tripPhotoDetails.drive_folder_id) {
      console.error('[API] No folder ID found for this trip');
      return NextResponse.json(
        { message: 'Could not determine the Google Drive folder ID. Please update the trip photo details with a valid Google Drive folder link.' },
        { status: 400 }
      );
    }
    
    console.log(`[API] Using folder ID: ${tripPhotoDetails.drive_folder_id}`);

    // Validate folder access before attempting to upload
    console.log(`[API] Validating access to folder: ${tripPhotoDetails.google_drive_link}`);
    const hasAccess = await validateFolderAccess(tripPhotoDetails.google_drive_link);
    if (!hasAccess) {
      console.error('[API] Service account does not have access to the specified folder');
      return NextResponse.json(
        { message: 'Cannot access the specified Google Drive folder. Make sure the service account has been granted access.' },
        { status: 403 }
      );
    }

    // The image path in the public directory
    const imagePath = path.join(
      process.cwd(),
      'public',
      imageUrl
    );

    console.log(`[API] Checking if image exists at: ${imagePath}`);
    // Check if the file exists
    if (!fs.existsSync(imagePath)) {
      console.error(`[API] Image file not found at path: ${imagePath}`);
      return NextResponse.json(
        { message: 'Image file not found' },
        { status: 404 }
      );
    }
    console.log('[API] Image file found');

    // Extract the filename from the imageUrl
    const fileName = path.basename(imageUrl);
    console.log(`[API] Uploading file with name: ${fileName}`);
    
    // Determine MIME type based on file extension
    const ext = path.extname(fileName).toLowerCase();
    let mimeType = 'image/jpeg'; // Default
    if (ext === '.png') mimeType = 'image/png';
    if (ext === '.gif') mimeType = 'image/gif';
    if (ext === '.webp') mimeType = 'image/webp';
    console.log(`[API] Detected MIME type: ${mimeType}`);

    // Upload the file to Google Drive using the folder ID
    console.log('[API] Starting file upload to Google Drive');
    const driveFileLink = await uploadToGoogleDrive(
      imagePath,
      fileName,
      mimeType,
      tripPhotoDetails.drive_folder_id
    );

    console.log(`[API] Upload successful. Drive file link: ${driveFileLink}`);
    
    // Cleanup: Remove the local file after successful upload
    try {
      await fs.remove(imagePath);
      console.log(`[API] Removed local file after successful upload: ${imagePath}`);
    } catch (cleanupError) {
      console.error('[API] Error removing local file:', cleanupError);
      // Continue even if cleanup fails
    }
    
    return NextResponse.json({
      message: 'Image uploaded to Google Drive successfully',
      driveFileLink,
      folderLink: tripPhotoDetails.google_drive_link
    });
  } catch (error: any) {
    console.error('[API] Error uploading to Google Drive:', error);
    return NextResponse.json(
      { message: 'Failed to upload to Google Drive', error: error.message },
      { status: 500 }
    );
  }
} 