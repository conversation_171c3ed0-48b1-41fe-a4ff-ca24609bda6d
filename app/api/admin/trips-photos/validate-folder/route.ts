import { NextRequest, NextResponse } from 'next/server';
import { 
  validateFolderAccess, 
  extractFolderIdFromUrl, 
  isGooglePhotosLink, 
  isGoogleDriveLink 
} from '@/lib/google-drive';

export async function POST(req: NextRequest) {
  console.log('[API] Starting storage validation process');
  try {
    const body = await req.json();
    const { folderUrl, folderId } = body;

    // Handle either folder URL or folder ID
    const folderIdentifier = folderId || folderUrl;

    if (!folderIdentifier) {
      console.error('[API] No folder URL or ID provided for validation');
      return NextResponse.json(
        { valid: false, message: 'No storage URL or ID provided' },
        { status: 400 }
      );
    }

    console.log(`[API] Validating access for: ${folderIdentifier}`);
    
    // Check if it's a Google Photos link
    if (typeof folderIdentifier === 'string' && isGooglePhotosLink(folderIdentifier)) {
      console.log('[API] Google Photos link detected, currently we consider these valid by default');
      // For now, we'll consider Google Photos links as valid
      // In a future implementation, we could validate Google Photos album access
      return NextResponse.json({
        valid: true,
        message: 'Google Photos album link accepted',
        storageType: 'google_photos',
        folderId: null
      });
    }
    
    // Extract folder ID from URL if it's a Google Drive URL
    let extractedFolderId = folderIdentifier;
    if (typeof folderIdentifier === 'string' && isGoogleDriveLink(folderIdentifier)) {
      extractedFolderId = extractFolderIdFromUrl(folderIdentifier) || '';
      if (!extractedFolderId) {
        return NextResponse.json({
          valid: false,
          message: 'Could not extract folder ID from the provided URL',
          storageType: 'google_drive',
          folderId: null
        });
      }
    }
    
    // Validate Google Drive folder access
    const isValid = await validateFolderAccess(folderIdentifier);
    
    if (isValid) {
      console.log('[API] Folder access validation successful');
      return NextResponse.json({
        valid: true,
        message: 'Service account has access to the folder',
        storageType: 'google_drive',
        folderId: extractedFolderId
      });
    } else {
      console.log('[API] Folder access validation failed');
      return NextResponse.json({
        valid: false,
        message: 'Service account does not have access to the folder',
        storageType: 'google_drive',
        folderId: extractedFolderId
      });
    }
  } catch (error: any) {
    console.error('[API] Error validating storage access:', error);
    return NextResponse.json(
      { 
        valid: false, 
        message: 'Failed to validate storage access',
        error: error.message 
      },
      { status: 500 }
    );
  }
} 