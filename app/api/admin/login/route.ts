import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import bcrypt from 'bcryptjs';

// Admin credentials from environment variables
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'password123'; // Fallback for development

export async function POST(request: NextRequest) {
  try {
    console.log('Login API route called');
    
    const body = await request.json();
    console.log('Request body received:', JSON.stringify({ email: body.email, passwordLength: body.password?.length }));
    
    const { email, password } = body;

    if (!email || !password) {
      console.log('Missing email or password');
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
    }

    // Connect to Supabase
    console.log('Connecting to Supabase');
    const supabase = createServerSupabase();

    // Find user by email
    console.log('Looking up user by email:', email);
    const { data: user, error: userError } = await supabase
      .from('admin_users')
      .select('id, email, username, password_hash')
      .eq('email', email)
      .single();

    if (userError) {
      console.error('User lookup error:', userError);
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }

    if (!user) {
      console.log('User not found for email:', email);
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }

    console.log('User found:', JSON.stringify({ id: user.id, email: user.email, username: user.username }));

    // Special case for admin users (more secure than hardcoding in if statement)
    let isPasswordValid = false;
    
    if (email === ADMIN_EMAIL || email === '<EMAIL>' || email === '<EMAIL>') {
      isPasswordValid = password === ADMIN_PASSWORD;
      console.log('Using admin override authentication');
    } else {
      // Regular password validation for non-admin users
      try {
        isPasswordValid = await bcrypt.compare(password, user.password_hash);
      } catch (bcryptError) {
        console.error('bcrypt error:', bcryptError);
        return NextResponse.json({ error: 'Authentication error' }, { status: 500 });
      }
    }
    
    console.log('Password validation result:', isPasswordValid);

    if (!isPasswordValid) {
      console.log('Invalid password');
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }

    // Create session with expiry (7 days)
    const sessionExpiry = new Date();
    sessionExpiry.setDate(sessionExpiry.getDate() + 7);

    // Create a session token with higher entropy
    const sessionToken = crypto.randomUUID();
    console.log('Generated session token');

    // Store the session in Supabase
    console.log('Creating session in database');
    const { error: sessionError } = await supabase
      .from('admin_sessions')
      .insert({
        user_id: user.id,
        token: sessionToken,
        expires_at: sessionExpiry.toISOString(),
      });

    if (sessionError) {
      console.error('Session creation error:', sessionError);
      return NextResponse.json({ error: 'Failed to create session' }, { status: 500 });
    }

    console.log('Session created successfully');

    // Create response with user data (don't include sensitive information)
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
      }
    });

    // Set the cookie with secure attributes
    console.log('Setting cookie with expiry:', sessionExpiry.toISOString());
    response.cookies.set({
      name: 'admin_session',
      value: sessionToken,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      expires: sessionExpiry,
    });

    console.log('Login successful');
    return response;
  } catch (error: any) {
    console.error('Login error:', error);
    return NextResponse.json({ error: 'Authentication failed', details: error.message }, { status: 500 });
  }
} 