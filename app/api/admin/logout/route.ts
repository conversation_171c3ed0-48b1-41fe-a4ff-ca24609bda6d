import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from the cookie
    const sessionToken = request.cookies.get('admin_session')?.value;
    
    if (sessionToken) {
      // Connect to Supabase
      const supabase = createServerSupabase();
      
      // Delete the session from the database using raw SQL
      await supabase.from('admin_sessions')
        .delete()
        .eq('token', sessionToken);
    }
    
    // Create response that clears the cookie
    const response = NextResponse.json({ success: true });
    
    // Clear the cookie
    response.cookies.set({
      name: 'admin_session',
      value: '',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      expires: new Date(0), // Set expiry to epoch time to delete the cookie
    });
    
    return response;
  } catch (error: any) {
    console.error('Logout error:', error);
    return NextResponse.json({ error: 'Logout failed' }, { status: 500 });
  }
} 