import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/inquiries/[id] - Get a single inquiry
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = createServerSupabase();
    const { data: inquiry, error } = await supabase
      .from('inquiries')
      .select('*, trips(title, slug)')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching inquiry:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiry' },
        { status: 500 }
      );
    }

    if (!inquiry) {
      return NextResponse.json(
        { error: 'Inquiry not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: inquiry });
  } catch (error) {
    console.error('Error in GET /api/admin/inquiries/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/inquiries/[id] - Update an inquiry
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = createServerSupabase();
    const body = await request.json();

    const updates: any = {};
    
    // Only update specified fields
    if (body.status) updates.status = body.status;
    if (body.admin_notes !== undefined) updates.admin_notes = body.admin_notes;
    
    // If status is being updated to anything other than 'new', set responded_at if not already set
    if (body.status && body.status !== 'new') {
      const { data: currentInquiry } = await supabase
        .from('inquiries')
        .select('responded_at')
        .eq('id', id)
        .single();

      if (!currentInquiry?.responded_at) {
        updates.responded_at = new Date().toISOString();
      }
    }

    // Update inquiry in database
    const { data: inquiry, error: dbError } = await supabase
      .from('inquiries')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (dbError) {
      console.error('Error updating inquiry:', dbError);
      return NextResponse.json(
        { error: 'Failed to update inquiry' },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: inquiry });
  } catch (error) {
    console.error('Error in PUT /api/admin/inquiries/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 