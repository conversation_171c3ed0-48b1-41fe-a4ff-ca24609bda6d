import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { InquiryStatus } from '@/types/inquiry';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/inquiries - Get all inquiries with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const tripId = searchParams.get('tripId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build query
    let query = supabase
      .from('inquiries')
      .select('*, trips(title)', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,message.ilike.%${search}%`);
    }
    if (status && status !== 'all') {
      // Validate that status is one of the allowed values
      const validStatus = ['new', 'in_progress', 'resolved', 'closed'].includes(status) 
        ? status as InquiryStatus 
        : 'new';
      query = query.eq('status', validStatus);
    }
    if (tripId && tripId !== 'all') {
      query = query.eq('trip_id', tripId);
    }
    
    // Apply date filters if provided
    if (startDate) {
      const startDateObj = new Date(startDate);
      startDateObj.setUTCHours(0, 0, 0, 0); // Start of the day
      query = query.gte('created_at', startDateObj.toISOString());
    }
    
    if (endDate) {
      const endDateObj = new Date(endDate);
      endDateObj.setUTCHours(23, 59, 59, 999); // End of the day
      query = query.lte('created_at', endDateObj.toISOString());
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: inquiries, error, count } = await query;

    if (error) {
      console.error('Error fetching inquiries:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: inquiries || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/admin/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/inquiries - Update inquiry status or add admin notes
export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const body = await request.json();

    // Basic validation
    if (!body.id) {
      return NextResponse.json(
        { error: 'Missing required field: id' },
        { status: 400 }
      );
    }

    const updates: any = {};
    
    // Only update specified fields
    if (body.status) {
      // Validate status is one of the allowed values
      const validStatus = ['new', 'in_progress', 'resolved', 'closed'].includes(body.status)
        ? body.status as InquiryStatus
        : 'new';
      updates.status = validStatus;
    }
    
    if (body.admin_notes !== undefined) updates.admin_notes = body.admin_notes;
    
    // If status is being updated to anything other than 'new', set responded_at if not already set
    if (body.status && body.status !== 'new' && !body.responded_at) {
      updates.responded_at = new Date().toISOString();
    }

    // Update inquiry in database
    const { data: inquiry, error: dbError } = await supabase
      .from('inquiries')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', body.id)
      .select()
      .single();

    if (dbError) {
      console.error('Error updating inquiry:', dbError);
      return NextResponse.json(
        { error: 'Failed to update inquiry' },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: inquiry });
  } catch (error) {
    console.error('Error in PUT /api/admin/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 