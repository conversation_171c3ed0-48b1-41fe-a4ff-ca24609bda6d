import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import bcrypt from 'bcryptjs';

// This endpoint should only be used in development
// For production, disable this endpoint or add proper authentication
export async function POST(request: NextRequest) {
  // Check if we're in development mode
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({ error: 'This endpoint is only available in development mode' }, { status: 403 });
  }

  try {
    const { email, password, username = email } = await request.json();

    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
    }

    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const password_hash = await bcrypt.hash(password, salt);

    // Connect to Supabase
    const supabase = createServerSupabase();

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('admin_users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      // Update existing user
      const { data, error } = await supabase
        .from('admin_users')
        .update({ password_hash })
        .eq('id', existingUser.id)
        .select('id, email');

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json({ 
        message: 'Admin user updated successfully', 
        user: data[0] 
      });
    } else {
      // Create new user
      const { data, error } = await supabase
        .from('admin_users')
        .insert({ 
          email, 
          username,
          password_hash 
        })
        .select('id, email');

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json({ 
        message: 'Admin user created successfully', 
        user: data[0] 
      });
    }
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 