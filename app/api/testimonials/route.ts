import { NextRequest, NextResponse } from 'next/server';
import type { CreateTestimonialData } from '@/types/database';
import { TESTIMONIALS } from '@/lib/constants';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// Convert constants testimonials to API format
const API_TESTIMONIALS = TESTIMONIALS.map(testimonial => ({
  id: testimonial.id.toString(),
  name: testimonial.name,
  rating: testimonial.rating,
  title: testimonial.role,
  content: testimonial.content,
  image_url: testimonial.image,
  is_featured: true,
  is_approved: true,
  created_at: new Date().toISOString(),
  trip_id: null
}));

// GET /api/testimonials - Get testimonials with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const featured = searchParams.get('featured');
    const tripId = searchParams.get('tripId');

    // Filter testimonials based on query parameters
    let filteredTestimonials = [...API_TESTIMONIALS];
    
    if (featured === 'true') {
      filteredTestimonials = filteredTestimonials.filter(t => t.is_featured);
    }
    
    if (tripId) {
      filteredTestimonials = filteredTestimonials.filter(t => t.trip_id === tripId);
    }

    // Apply pagination
    const total = filteredTestimonials.length;
    const totalPages = Math.ceil(total / limit);
    const from = (page - 1) * limit;
    const to = Math.min(from + limit, total);
    
    const paginatedTestimonials = filteredTestimonials.slice(from, to);

    return NextResponse.json({
      data: paginatedTestimonials,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/testimonials:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/testimonials - Create a new testimonial (stores only in memory for demo)
export async function POST(request: NextRequest) {
  try {
    const body: CreateTestimonialData = await request.json();

    // Basic validation
    const requiredFields = ['name', 'rating', 'content'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateTestimonialData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate rating
    if (body.rating < 1 || body.rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // Return success with mock data - not actually saved since we're using constants
    return NextResponse.json({
      message: 'Thank you for your feedback! Your testimonial has been submitted for review.',
      data: { id: 'temp-' + Date.now(), ...body, is_approved: false }
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/testimonials:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
