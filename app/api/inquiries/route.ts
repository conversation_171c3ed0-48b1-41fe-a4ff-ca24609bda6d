import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { sendInquiryNotification, InquiryEmailData } from '@/lib/email';
import type { CreateInquiryData } from '@/types/database';
import { InquiryStatus } from '@/types/inquiry';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/inquiries - Get inquiries (Admin only)
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const inquiryType = searchParams.get('inquiryType');

    // Build query
    let query = supabase
      .from('inquiries')
      .select(`
        id,
        name,
        email,
        phone,
        subject,
        message,
        inquiry_type,
        status,
        created_at
      `, { count: 'exact' });

    // Apply filters
    if (status && status !== 'all') {
      // Validate that status is one of the allowed values
      const validStatus = ['new', 'in_progress', 'resolved', 'closed'].includes(status) 
        ? status as InquiryStatus 
        : 'new';
      query = query.eq('status', validStatus);
    }
    if (inquiryType && inquiryType !== 'all') {
      query = query.eq('inquiry_type', inquiryType);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: inquiries, error, count } = await query;

    if (error) {
      console.error('Error fetching inquiries:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: inquiries || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/inquiries - Create a new inquiry
export async function POST(request: NextRequest) {
  try {
    const body: CreateInquiryData = await request.json();

    // Basic validation
    const requiredFields = ['name', 'email', 'message'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateInquiryData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // Save inquiry to database
    const { data: inquiry, error: dbError } = await supabase
      .from('inquiries')
      .insert({
        name: body.name,
        email: body.email,
        phone: body.phone || null,
        subject: body.subject || null,
        message: body.message,
        inquiry_type: body.inquiry_type || 'General Inquiry',
        status: 'new'
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving inquiry to database:', dbError);
      return NextResponse.json(
        { error: 'Failed to save inquiry' },
        { status: 500 }
      );
    }

    // Log the saved inquiry to terminal
    console.log('💾 INQUIRY SAVED TO DATABASE:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('Database Row:', JSON.stringify(inquiry, null, 2));
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Send email notification
    try {
      // Convert null values to undefined to match the InquiryEmailData type
      const emailData: InquiryEmailData = {
        id: inquiry.id,
        name: inquiry.name,
        email: inquiry.email,
        phone: inquiry.phone || undefined,
        subject: inquiry.subject || undefined,
        message: inquiry.message,
        inquiry_type: inquiry.inquiry_type || 'General Inquiry',
        created_at: inquiry.created_at || new Date().toISOString()
      };
      await sendInquiryNotification(emailData);
    } catch (emailError) {
      console.error('❌ Error sending email notification:', emailError);
      // Don't fail the request if email fails, just log it
    }

    return NextResponse.json({
      message: 'Inquiry submitted successfully. We will get back to you soon!',
      data: inquiry
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}


