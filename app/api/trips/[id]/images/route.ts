import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/trips/[id]/images - Get all images for a trip
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    // Return empty array for now (database removed)
    return NextResponse.json({
      data: [],
    });
  } catch (error) {
    console.error('Error in GET /api/trips/[id]/images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/trips/[id]/images - Upload images for a trip (Admin only)
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    // Return placeholder response
    return NextResponse.json({
      message: 'Image upload functionality temporarily disabled',
      data: null
    }, { status: 501 });
  } catch (error) {
    console.error('Error in POST /api/trips/[id]/images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/trips/[id]/images - Update image order and properties (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;
    const { images } = await request.json();

    // Return placeholder response
    return NextResponse.json({
      message: 'Image update functionality temporarily disabled',
    }, { status: 501 });
  } catch (error) {
    console.error('Error in PUT /api/trips/[id]/images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
