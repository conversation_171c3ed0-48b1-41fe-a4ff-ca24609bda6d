import { NextRequest, NextResponse } from 'next/server';
import type { CreateTripData } from '@/types/database';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/trips/[id] - Get a specific trip
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    // Return 404 for now (database removed)
    return NextResponse.json(
      { error: 'Trip not found' },
      { status: 404 }
    );
  } catch (error) {
    console.error('Error in GET /api/trips/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/trips/[id] - Update a trip (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;
    const body: Partial<CreateTripData> = await request.json();

    // Return placeholder response
    return NextResponse.json({
      message: 'Trip update functionality temporarily disabled',
      data: null
    }, { status: 501 });
  } catch (error) {
    console.error('Error in PUT /api/trips/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/trips/[id] - Delete a trip (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    // Return placeholder response
    return NextResponse.json({
      message: 'Trip deletion functionality temporarily disabled',
    }, { status: 501 });
  } catch (error) {
    console.error('Error in DELETE /api/trips/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
