'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  MapPin,
  Calendar,
  Users,
  Star,
  ArrowRight,
  Mountain
} from 'lucide-react';
import { cn, getDifficultyColor } from '@/lib/utils';
import type { Trip } from '@/types/database';

interface FeaturedTrip {
  id: string;
  title: string;
  slug: string;
  description: string | null;
  destination: string;
  days: number;
  nights: number;
  price_per_person: number;
  difficulty: 'easy' | 'moderate' | 'challenging' | 'extreme';
  featured_image_url: string | null;
  is_featured: boolean;
  is_active: boolean;
}

interface FeaturedTripsSectionProps {
  featuredTrips: FeaturedTrip[];
}

export default function FeaturedTripsSection({ featuredTrips }: FeaturedTripsSectionProps) {
  const [hoveredTrip, setHoveredTrip] = useState<string | null>(null);
  const isFallbackMode = featuredTrips.length === 0;

  // If there are no trips, don't render this section at all
  if (isFallbackMode) {
    return null;
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="section-title">
            Featured <span className="text-gradient">Destinations</span>
          </h2>
          <p className="section-subtitle">
            Discover our most popular educational tours and adventure experiences.
            Each destination is carefully selected to provide meaningful learning opportunities
            and unforgettable memories.
          </p>
        </motion.div>

        {/* Trips Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {featuredTrips.map((trip, index) => (
            <motion.div
              key={trip.id}
              variants={itemVariants}
              onMouseEnter={() => setHoveredTrip(trip.id)}
              onMouseLeave={() => setHoveredTrip(null)}
              className="group"
            >
              <div className="card overflow-hidden hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2">
                {/* Image */}
                <div className="relative h-64 overflow-hidden">
                  <Image
                    src={trip.featured_image_url || '/images/fallback-trip.jpg'}
                    alt={trip.title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Difficulty Badge */}
                  <div className="absolute top-4 right-4">
                    <span className={cn(
                      'px-3 py-1 rounded-full text-xs font-medium',
                      getDifficultyColor(trip.difficulty)
                    )}>
                      {trip.difficulty.charAt(0).toUpperCase() + trip.difficulty.slice(1)}
                    </span>
                  </div>

                  {/* Quick Info Overlay */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                      opacity: hoveredTrip === trip.id ? 1 : 0,
                      y: hoveredTrip === trip.id ? 0 : 20
                    }}
                    transition={{ duration: 0.3 }}
                    className="absolute bottom-4 left-4 right-4"
                  >
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-1 text-gray-700">
                          <Calendar className="h-4 w-4" />
                          <span>{trip.days} Days, {trip.nights} Nights</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors border-b-2 border-primary-600 pb-1 inline-block">
                      {trip.title}
                    </h3>
                  </div>

                  <div className="flex items-center space-x-1 mb-3 text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span className="text-sm">{trip.destination}</span>
                  </div>

                  <p className="text-gray-600 mb-4 line-clamp-3 leading-relaxed">
                    {trip.description || 'Discover amazing educational experiences and create unforgettable memories.'}
                  </p>

                  {/* Trip Details */}
                  <div className="flex items-center justify-between mb-6 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{trip.days} Days, {trip.nights} Nights</span>
                    </div>
                  </div>

                  {/* CTA */}
                  <Link
                    href={`/trips/${trip.slug}`}
                    className="inline-flex items-center justify-center w-full px-6 py-3 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-300 transform hover:scale-105 group"
                  >
                    Explore Trip
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
