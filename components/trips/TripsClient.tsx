'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  MapPin,
  Calendar,
  Mountain,
  Star,
  Search
} from 'lucide-react'

interface Trip {
  id: string
  title: string
  slug: string
  destination: string
  days: number
  nights: number
  price_per_person: number
  difficulty: 'easy' | 'moderate' | 'challenging' | 'extreme'
  featured_image_url: string | null
  description: string | null
  is_featured: boolean
  is_active: boolean
  is_trek: boolean
}

interface TripsClientProps {
  trips: Trip[]
}

// Utility function to get difficulty color
const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'easy':
      return 'bg-green-100 text-green-800';
    case 'moderate':
      return 'bg-yellow-100 text-yellow-800';
    case 'challenging':
      return 'bg-orange-100 text-orange-800';
    case 'extreme':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-blue-100 text-blue-800';
  }
};

// Format price to Indian Rupees
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0
  }).format(price);
};

export default function TripsClient({ trips }: TripsClientProps) {
  const [searchQuery, setSearchQuery] = useState('')
  
  // Filter trips based on search
  const filteredTrips = trips.filter(trip => {
    if (!searchQuery) return true;
    
    return (
      trip.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      trip.destination.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (trip.description && trip.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  });
  
  // Sort trips - featured trips first
  const sortedTrips = [...filteredTrips].sort((a, b) => {
    if (a.is_featured && !b.is_featured) return -1;
    if (!a.is_featured && b.is_featured) return 1;
    return 0;
  });

  return (
    <div className="space-y-8">
      {/* Search Header */}
      <div className="text-center">
        <motion.h1 
          className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {searchQuery ? `Search Results for "${searchQuery}"` : 'Explore Our Educational Tours'}
        </motion.h1>
        <motion.p 
          className="text-xl text-gray-600 max-w-3xl mx-auto mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          Discover amazing educational tours and adventures across India
        </motion.p>

        {/* Search Bar */}
        <motion.div 
          className="max-w-2xl mx-auto relative mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search destinations, activities, or trip types..."
            className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
          />
        </motion.div>
      </div>

      {/* Trips Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedTrips.length > 0 ? (
          sortedTrips.map((trip, index) => (
            <TripCard
              key={trip.id}
              trip={trip}
              index={index}
            />
          ))
        ) : (
          <div className="col-span-3 bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
            <div className="text-gray-400 mb-4">
              <Search className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">No trips found</h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your search criteria
            </p>
            <button
              onClick={() => setSearchQuery('')}
              className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors"
            >
              Clear Search
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

function TripCard({ trip, index }: { trip: Trip; index: number }) {
  // Animation variants
  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5
      }
    })
  };

  return (
    <motion.div
      custom={index}
      initial="hidden"
      animate="visible"
      variants={variants}
      className="group"
    >
      <Link href={`/trips/${trip.slug}`}>
        <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 border border-gray-200">
          {/* Image */}
          <div className="relative h-48 overflow-hidden">
            <Image
              src={trip.featured_image_url || '/images/fallback-trip.jpg'}
              alt={trip.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            
            {/* Difficulty Badge */}
            <div className="absolute top-3 left-3">
              <span className={`px-3 py-1 text-xs font-medium rounded-full capitalize ${getDifficultyColor(trip.difficulty)}`}>
                {trip.difficulty}
              </span>
            </div>
            
            {/* Featured Badge */}
            {trip.is_featured && (
              <div className="absolute top-3 right-3">
                <span className="px-3 py-1 bg-yellow-400 text-yellow-800 text-xs font-medium rounded-full flex items-center gap-1">
                  <Star className="w-3 h-3" />
                  Featured
                </span>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-6 flex flex-col">
            {/* Location */}
            <div className="flex items-center gap-1 text-blue-600 text-sm mb-2">
              <MapPin className="w-4 h-4" />
              {trip.destination}
            </div>
            
            {/* Title */}
            <h3 className="font-bold text-xl text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
              {trip.title}
            </h3>
            
            {/* Description */}
            {trip.description && (
              <p className="text-gray-600 mb-4 text-sm line-clamp-2">
                {trip.description}
              </p>
            )}
            
            {/* Trip details */}
            <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>{trip.days} {trip.days === 1 ? 'Day' : 'Days'}</span>
                {trip.nights > 0 && <span>, {trip.nights} {trip.nights === 1 ? 'Night' : 'Nights'}</span>}
              </div>
            </div>
            
            {/* Trek indicator */}
            {trip.is_trek && (
              <div className="flex items-center gap-1 text-sm text-green-600 mb-3">
                <Mountain className="w-4 h-4" />
                <span>Trek</span>
              </div>
            )}
            
            {/* Price - pushed to bottom with flex-1 spacer */}
            <div className="mt-auto">
              <div className="font-bold text-xl text-gray-900">
                {formatPrice(trip.price_per_person)}
                <span className="text-sm font-normal text-gray-600 ml-1">per person</span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
