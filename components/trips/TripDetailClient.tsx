'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Star,
  ArrowLeft,
  Heart,
  Share2,
  Mountain,
  CheckCircle,
  XCircle,
  Info,
  Shield,
  Package,
  Activity,
  Train,
  Home,
  Phone,
  CreditCard,
  AlertTriangle,
  Backpack,
  ImageIcon,
  FileText,
  DollarSign,
  Tag,
  Utensils,
  Coffee,
  UtensilsCrossed,
  Bed,
  ArrowRight,
  Compass,
  Truck,
  Sparkles,
  Bike,
  Navigation,
  Map,
  ChevronRight
} from 'lucide-react';
import Button from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { JsonLd } from '@/components/seo/JsonLd';

// Define types for itinerary details
interface ItineraryDetails {
  title: string;
  description: string;
  location?: string;
  accommodation?: string;
  meals?: string;
}

// Define tab interface
interface Tab {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

interface Trip {
  id: string;
  title: string;
  slug: string;
  description: string;
  detailed_description: string;
  destination: string;
  days: number;
  nights: number;
  price_per_person: number;
  difficulty: 'easy' | 'moderate' | 'challenging' | 'extreme';
  featured_image_url: string;
  is_trek: boolean;
  mode_of_travel?: string;
  pickup_location?: string;
  drop_location?: string;
  property_used?: string;
  min_age?: number;
  max_age?: number;
  category?: string;
  itinerary: any; // Using any to match jsonb type from Supabase
  activities: string[];
  optional_activities: string[];
  inclusions: string[];
  exclusions: string[];
  benefits: string[];
  safety_supervision: string[];
  things_to_carry: string[];
  special_notes?: string[];
  payment_terms?: string;
  cancellation_policy?: any; // Using any to match jsonb type from Supabase
  available_dates?: string[];
  commercial_price?: number;
  available_from?: string;
  available_to?: string;
}

interface TripDetailClientProps {
  trip: Trip;
  relatedTrips: Trip[];
}

export default function TripDetailClient({ trip, relatedTrips }: TripDetailClientProps) {
  const [activeTab, setActiveTab] = useState('travel-info');
  const [activeItineraryDay, setActiveItineraryDay] = useState<number>(1);
  
  // Helper function to check if an array has items
  const hasLength = (arr?: any[] | null): boolean => {
    return !!arr && arr.length > 0;
  };
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Tab content animation variants
  const tabContentVariants = {
    hidden: { opacity: 0, x: 10 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.3 } }
  };

  // Helper function to render meal icons
  const renderMealIcons = (meals: string[] | { breakfast?: boolean; lunch?: boolean; dinner?: boolean } | undefined) => {
    if (!meals) return null;
    
    // Handle array format
    if (Array.isArray(meals)) {
      return (
        <div className="flex items-center gap-2 mt-2">
          {meals.includes('Breakfast') && (
            <div className="flex items-center gap-1 bg-blue-50 px-2 py-1 rounded-md">
              <Coffee className="w-3 h-3 text-blue-600" />
              <span className="text-xs text-blue-600">Breakfast</span>
            </div>
          )}
          {meals.includes('Lunch') && (
            <div className="flex items-center gap-1 bg-green-50 px-2 py-1 rounded-md">
              <Utensils className="w-3 h-3 text-green-600" />
              <span className="text-xs text-green-600">Lunch</span>
            </div>
          )}
          {meals.includes('Dinner') && (
            <div className="flex items-center gap-1 bg-purple-50 px-2 py-1 rounded-md">
              <UtensilsCrossed className="w-3 h-3 text-purple-600" />
              <span className="text-xs text-purple-600">Dinner</span>
            </div>
          )}
        </div>
      );
    }
    
    // Handle object format
    return (
      <div className="flex items-center gap-2 mt-2">
        {meals.breakfast && (
          <div className="flex items-center gap-1 bg-blue-50 px-2 py-1 rounded-md">
            <Coffee className="w-3 h-3 text-blue-600" />
            <span className="text-xs text-blue-600">Breakfast</span>
          </div>
        )}
        {meals.lunch && (
          <div className="flex items-center gap-1 bg-green-50 px-2 py-1 rounded-md">
            <Utensils className="w-3 h-3 text-green-600" />
            <span className="text-xs text-green-600">Lunch</span>
          </div>
        )}
        {meals.dinner && (
          <div className="flex items-center gap-1 bg-purple-50 px-2 py-1 rounded-md">
            <UtensilsCrossed className="w-3 h-3 text-purple-600" />
            <span className="text-xs text-purple-600">Dinner</span>
          </div>
        )}
      </div>
    );
  };

  // Tabs configuration with icons
  const tabs: Tab[] = [
    { id: 'travel-info', label: 'Travel Info', icon: <Train className="w-5 h-5" /> },
    { id: 'inclusions', label: 'Inclusions', icon: <CheckCircle className="w-5 h-5" /> },
    { id: 'activities', label: 'Activities', icon: <Activity className="w-5 h-5" /> },
    { id: 'things-to-carry', label: 'Things to Carry', icon: <Backpack className="w-5 h-5" /> },
    { id: 'safety', label: 'Safety & Benefits', icon: <Shield className="w-5 h-5" /> },
  ];

  // Function to check if a tab has content
  const hasTabContent = (tabId: string) => {
    switch (tabId) {
      case 'travel-info':
        return !!(trip.mode_of_travel || trip.pickup_location || trip.drop_location || trip.property_used);
      case 'inclusions':
        return (trip.inclusions && trip.inclusions.length > 0) || (trip.exclusions && trip.exclusions.length > 0);
      case 'activities':
        return (trip.activities && trip.activities.length > 0) || (trip.optional_activities && trip.optional_activities.length > 0);
      case 'things-to-carry':
        return hasLength(trip.things_to_carry);
      case 'safety':
        return hasLength(trip.benefits) || hasLength(trip.safety_supervision);
      default:
        return false;
    }
  };

  // Set initial active tab to the first one that has content
  useEffect(() => {
    for (const tab of tabs) {
      if (hasTabContent(tab.id)) {
        setActiveTab(tab.id);
        break;
      }
    }
  }, []);

  // Format price to Indian Rupees
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(price);
  };

  // Utility function to get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'moderate':
        return 'bg-yellow-100 text-yellow-800';
      case 'challenging':
        return 'bg-orange-100 text-orange-800';
      case 'extreme':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  // Create the structured data for JSON-LD
  const tourData = {
    "@context": "https://schema.org",
    "@type": "TouristTrip",
    "name": trip.title,
    "description": trip.description,
    "touristType": ["Educational", trip.is_trek ? "Adventure" : "Leisure"],
    "offers": {
      "@type": "Offer",
      "price": trip.price_per_person,
      "priceCurrency": "INR",
      "availability": "https://schema.org/InStock"
    },
    "itinerary": {
      "@type": "ItemList",
      "numberOfItems": trip.days,
      "itemListElement": Object.entries(trip.itinerary || {}).map(([day, details], index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "TouristDestination",
          "name": (details as any)?.title || `Day ${index + 1}`,
          "description": (details as any)?.description || ""
        }
      }))
    },
    "subjectOf": {
      "@type": "CreativeWork",
      "abstract": trip.detailed_description
    },
    "provider": {
      "@type": "Organization",
      "name": "Positive7 Educational Tours",
      "url": "https://positive7.org"
    }
  };

  return (
    <>
      {/* Add JSON-LD structured data */}
      <JsonLd data={tourData} />

      <div className="flex-1">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50"
        >
          {/* Hero Section - Full viewport width */}
          <motion.section variants={itemVariants} className="relative h-[50vh] overflow-hidden w-full">
            <Image
              src={trip.featured_image_url || '/images/fallback-trip.jpg'}
              alt={trip.title}
              fill
              className="object-cover w-full"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />

            {/* Navigation */}
            <div className="absolute top-6 left-6 z-10">
              <Link href="/trips">
                <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Trips
                </Button>
              </Link>
            </div>

            {/* Action Buttons */}
            <div className="absolute top-6 right-6 z-10 flex gap-2">
              <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
                <Heart className="w-4 h-4" />
              </Button>
              <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
                <Share2 className="w-4 h-4" />
              </Button>
            </div>

            {/* Hero Content */}
            <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
              <div className="max-w-7xl mx-auto">
                <motion.div
                  variants={itemVariants}
                  className="flex flex-wrap items-end justify-between gap-6"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-4 mb-4">
                      <span className="px-3 py-1 bg-blue-600 rounded-full text-sm font-medium">
                        {trip.destination}
                      </span>
                    </div>
                    <h1 className="text-4xl md:text-6xl font-bold mb-2">{trip.title}</h1>
                    <p className="text-xl text-white/90 mb-4">{trip.destination}</p>
                    <div className="flex flex-wrap items-center gap-6 text-white/80">
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        <span>{trip.days} Days, {trip.nights} Nights</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        <span>Educational Tour</span>
                      </div>
                      {trip.is_trek && (
                        <div className="flex items-center gap-2 bg-green-600/80 px-2 py-1 rounded-md">
                          <Mountain className="w-4 h-4" />
                          <span>Trek</span>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Mountain className="w-5 h-5" />
                        <span className="capitalize">{trip.difficulty}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.section>

          {/* Main Content */}
          <div className="max-w-7xl mx-auto px-4 py-12">
            <div className="grid lg:grid-cols-3 gap-12">
              {/* Left Column - Main Content */}
              <div className="lg:col-span-2 space-y-12">
                {/* Trip Overview */}
                <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-xl">
                  <h2 className="text-3xl font-bold mb-6 text-gray-900">Trip Overview</h2>
                  <p className="text-gray-700 text-lg leading-relaxed mb-8">
                    {trip.description || 'Discover amazing educational experiences and create unforgettable memories with Positive7.'}
                  </p>

                  {/* Quick Info Grid */}
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Clock className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Duration</div>
                          <div className="text-gray-600">{trip.days} Days, {trip.nights} Nights</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Users className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Age Range</div>
                          <div className="text-gray-600">
                            {trip.min_age && trip.max_age 
                              ? `${trip.min_age} - ${trip.max_age} years` 
                              : trip.min_age 
                                ? `${trip.min_age}+ years` 
                                : trip.max_age 
                                  ? `Up to ${trip.max_age} years` 
                                  : 'All ages'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Mountain className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Difficulty</div>
                          <div className="text-gray-600 capitalize">{trip.difficulty}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <MapPin className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Destination</div>
                          <div className="text-gray-600">{trip.destination}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.section>

                {/* Trip Details */}
                {trip.detailed_description && (
                  <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-xl">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900">About This Trip</h2>
                    <p className="text-gray-700 text-lg leading-relaxed">
                      {trip.detailed_description}
                    </p>
                  </motion.section>
                )}

                {/* Itinerary with day tabs */}
                {trip.itinerary && Object.keys(trip.itinerary).length > 0 && (
                  <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-xl">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900 flex items-center gap-3">
                      <FileText className="w-8 h-8 text-green-600" />
                      Day-wise Itinerary
                    </h2>
                    
                    {/* Day tabs */}
                    <div className="flex overflow-x-auto mb-6 pb-2 -mx-2 px-2 space-x-2 scrollbar-hide">
                      {Object.entries(trip.itinerary).map(([day, details], index) => (
                        <button
                          key={day}
                          onClick={() => setActiveItineraryDay(index + 1)}
                          className={`flex-shrink-0 flex items-center px-4 py-2 rounded-full transition-colors ${
                            activeItineraryDay === index + 1
                              ? 'bg-green-500 text-white font-medium shadow-md'
                              : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                          }`}
                        >
                          <span className="font-medium">Day {index + 1}</span>
                        </button>
                      ))}
                    </div>
                    
                    {/* Display active day content */}
                    <div className="relative overflow-hidden min-h-[200px]">
                      {Object.entries(trip.itinerary || {}).map(([day, details], index) => (
                        <motion.div 
                          key={day} 
                          initial="hidden"
                          animate={index + 1 === activeItineraryDay ? "visible" : "hidden"}
                          variants={tabContentVariants}
                          className={`border-l-4 border-green-500 pl-6 pb-6 ${index + 1 === activeItineraryDay ? 'block' : 'hidden'}`}
                        >
                          <div className="flex items-center gap-3 mb-3">
                            <div className="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                              {index + 1}
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900">
                              {(details as any)?.title || `Day ${index + 1}`}
                            </h3>
                          </div>
                          <p className="text-gray-700 leading-relaxed mb-3">
                            {(details as any)?.description}
                          </p>
                          
                          {/* Additional Details */}
                          <div className="mt-3 space-y-2">
                            {/* Accommodation */}
                            {(details as any)?.accommodation && (
                              <div className="flex items-center gap-2">
                                <Bed className="w-4 h-4 text-indigo-600" />
                                <span className="text-sm text-gray-700">
                                  <span className="font-medium">Accommodation:</span> {(details as any)?.accommodation}
                                </span>
                              </div>
                            )}
                            
                            {/* Activities */}
                            {(details as any)?.activities && (details as any)?.activities.length > 0 && (
                              <div className="mt-2">
                                <div className="flex items-center gap-2 mb-1">
                                  <Activity className="w-4 h-4 text-amber-600" />
                                  <span className="text-sm font-medium text-gray-700">Activities:</span>
                                </div>
                                <div className="flex flex-wrap gap-2 ml-6">
                                  {(details as any)?.activities.map((activity: string, actIdx: number) => (
                                    <span key={actIdx} className="text-xs bg-amber-50 text-amber-700 px-2 py-1 rounded-md">
                                      {activity}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {/* Meals */}
                            {(details as any)?.meals && (
                              <div className="mt-2">
                                {renderMealIcons((details as any)?.meals)}
                              </div>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.section>
                )}

                {/* Smart Tab Component for Multiple Sections */}
                {(hasLength(trip.activities) || hasLength(trip.optional_activities) || 
                  hasLength(trip.inclusions) || hasLength(trip.exclusions) || 
                  trip.mode_of_travel || trip.pickup_location || trip.drop_location || trip.property_used ||
                  hasLength(trip.benefits) || hasLength(trip.safety_supervision) || 
                  hasLength(trip.things_to_carry)) && (
                  <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-xl">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900">Trip Details</h2>
                    
                    {/* Tab Navigation - Make sure it's a single row */}
                    <div className="flex flex-wrap justify-center gap-3 sm:gap-4 mb-8 border-b pb-6 overflow-x-auto no-scrollbar">
                      <div className="flex flex-row flex-wrap justify-center w-full gap-3 sm:gap-4">
                        {tabs.map((tab) => (
                          hasTabContent(tab.id) && (
                            <button
                              key={tab.id}
                              onClick={() => setActiveTab(tab.id)}
                              className={`flex flex-col items-center p-3 sm:p-4 rounded-lg transition-all ${
                                activeTab === tab.id
                                  ? 'bg-blue-50 text-blue-600 transform scale-105 shadow-md border border-blue-200'
                                  : 'text-gray-500 hover:bg-gray-50'
                              }`}
                              aria-selected={activeTab === tab.id}
                              role="tab"
                            >
                              <div className={`${activeTab === tab.id ? 'text-blue-600' : 'text-gray-400'} mb-2 w-6 h-6`}>
                                {tab.icon}
                              </div>
                              <span className="text-xs sm:text-sm font-medium whitespace-nowrap">{tab.label}</span>
                            </button>
                          )
                        ))}
                      </div>
                    </div>

                    {/* Tab Content - with increased top padding */}
                    <div className="relative min-h-[200px] mt-6 pt-2">
                      {/* Travel Information Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'travel-info' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'travel-info' ? 'block' : 'hidden'}
                      >
                        <div className="grid md:grid-cols-2 gap-6">
                          {trip.mode_of_travel && (
                            <div className="flex items-start gap-3">
                              <Train className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                              <div>
                                <div className="font-medium text-gray-900 mb-1">Mode of Travel</div>
                                <div className="text-gray-600">{trip.mode_of_travel}</div>
                              </div>
                            </div>
                          )}
                          {trip.pickup_location && (
                            <div className="flex items-start gap-3">
                              <MapPin className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                              <div>
                                <div className="font-medium text-gray-900 mb-1">Pickup Location</div>
                                <div className="text-gray-600">{trip.pickup_location}</div>
                              </div>
                            </div>
                          )}
                          {trip.drop_location && (
                            <div className="flex items-start gap-3">
                              <MapPin className="w-5 h-5 text-red-600 mt-1 flex-shrink-0" />
                              <div>
                                <div className="font-medium text-gray-900 mb-1">Drop Location</div>
                                <div className="text-gray-600">{trip.drop_location}</div>
                              </div>
                            </div>
                          )}
                          {trip.property_used && (
                            <div className="flex items-start gap-3">
                              <Home className="w-5 h-5 text-purple-600 mt-1 flex-shrink-0" />
                              <div>
                                <div className="font-medium text-gray-900 mb-1">Accommodation</div>
                                <div className="text-gray-600">{trip.property_used}</div>
                              </div>
                            </div>
                          )}
                        </div>
                      </motion.div>

                      {/* Activities Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'activities' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'activities' ? 'block' : 'hidden'}
                      >
                        {trip.activities && trip.activities.length > 0 && (
                          <div>
                            <div className="grid md:grid-cols-2 gap-4">
                              {trip.activities.map((activity, index) => (
                                <div key={index} className="flex items-center gap-3">
                                  <Activity className="w-5 h-5 text-green-600" />
                                  <span className="text-gray-700">{activity}</span>
                                </div>
                              ))}
                            </div>

                            {trip.optional_activities && trip.optional_activities.length > 0 && (
                              <div className="mt-8">
                                <h3 className="text-xl font-semibold mb-4 text-gray-900">Optional Activities</h3>
                                <div className="grid md:grid-cols-2 gap-4">
                                  {trip.optional_activities.map((activity, index) => (
                                    <div key={index} className="flex items-center gap-3">
                                      <Activity className="w-5 h-5 text-orange-600" />
                                      <span className="text-gray-700">{activity}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </motion.div>

                      {/* Inclusions & Exclusions Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'inclusions' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'inclusions' ? 'block' : 'hidden'}
                      >
                        <div className="grid md:grid-cols-2 gap-8">
                          {/* Inclusions */}
                          <div>
                            <h4 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                              <CheckCircle className="w-5 h-5" />
                              Included
                            </h4>
                            <div className="space-y-3">
                              {trip.inclusions && trip.inclusions.map((item, index) => (
                                <div key={index} className="flex items-start gap-3">
                                  <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                                  <span className="text-gray-700">{item}</span>
                                </div>
                              ))}
                              {(!trip.inclusions || trip.inclusions.length === 0) && (
                                <p className="text-gray-500 italic">No inclusions specified</p>
                              )}
                            </div>
                          </div>

                          {/* Exclusions */}
                          <div>
                            <h4 className="text-xl font-semibold mb-4 text-red-700 flex items-center gap-2">
                              <XCircle className="w-5 h-5" />
                              Not Included
                            </h4>
                            <div className="space-y-3">
                              {trip.exclusions && trip.exclusions.map((item, index) => (
                                <div key={index} className="flex items-start gap-3">
                                  <XCircle className="w-4 h-4 text-red-600 mt-1 flex-shrink-0" />
                                  <span className="text-gray-700">{item}</span>
                                </div>
                              ))}
                              {(!trip.exclusions || trip.exclusions.length === 0) && (
                                <p className="text-gray-500 italic">No exclusions specified</p>
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>

                      {/* Safety & Benefits Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'safety' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'safety' ? 'block' : 'hidden'}
                      >
                        <div className="grid md:grid-cols-2 gap-8">
                          {/* Benefits */}
                          {trip.benefits && trip.benefits.length > 0 && (
                            <div>
                              <h4 className="text-xl font-semibold mb-4 text-blue-700 flex items-center gap-2">
                                <Package className="w-5 h-5" />
                                Trip Benefits
                              </h4>
                              <div className="space-y-3">
                                {trip.benefits.map((benefit, index) => (
                                  <div key={index} className="flex items-start gap-3">
                                    <CheckCircle className="w-4 h-4 text-blue-600 mt-1 flex-shrink-0" />
                                    <span className="text-gray-700">{benefit}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Safety */}
                          {trip.safety_supervision && trip.safety_supervision.length > 0 && (
                            <div>
                              <h4 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                                <Shield className="w-5 h-5" />
                                Safety & Supervision
                              </h4>
                              <div className="space-y-3">
                                {trip.safety_supervision.map((safety, index) => (
                                  <div key={index} className="flex items-start gap-3">
                                    <Shield className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                                    <span className="text-gray-700">{safety}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          {(!trip.benefits || trip.benefits.length === 0) && (!trip.safety_supervision || trip.safety_supervision.length === 0) && (
                            <div className="col-span-2 text-center py-8">
                              <p className="text-gray-500 italic">No safety information or benefits specified</p>
                            </div>
                          )}
                        </div>
                      </motion.div>

                      {/* Things to Carry Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'things-to-carry' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'things-to-carry' ? 'block' : 'hidden'}
                      >
                        <h4 className="text-xl font-semibold mb-4 text-amber-700 flex items-center gap-2">
                          <Backpack className="w-5 h-5" />
                          Things to Carry
                        </h4>
                        {trip.things_to_carry && trip.things_to_carry.length > 0 ? (
                          <div className="grid md:grid-cols-2 gap-4">
                            {trip.things_to_carry.map((item, index) => (
                              <div key={index} className="flex items-start gap-3">
                                <Backpack className="w-4 h-4 text-amber-600 mt-1 flex-shrink-0" />
                                <span className="text-gray-700">{item}</span>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <p className="text-gray-500 italic">No items to carry specified</p>
                          </div>
                        )}
                      </motion.div>
                    </div>
                  </motion.section>
                )}

                {/* Special Notes */}
                {trip.special_notes && trip.special_notes.length > 0 && (
                  <motion.section variants={itemVariants} className="bg-yellow-50 border border-yellow-200 rounded-2xl p-8 shadow-xl">
                    <h2 className="text-2xl font-bold mb-6 text-yellow-800 flex items-center gap-3">
                      <Info className="w-6 h-6" />
                      Important Notes
                    </h2>
                    <div className="space-y-3">
                      {trip.special_notes.map((note, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <Info className="w-4 h-4 text-yellow-600 mt-1 flex-shrink-0" />
                          <span className="text-yellow-800">{note}</span>
                        </div>
                      ))}
                    </div>
                  </motion.section>
                )}

                {/* Payment Terms */}
                {trip.payment_terms && (
                  <motion.section variants={itemVariants} className="bg-blue-50 border border-blue-200 rounded-2xl p-8 shadow-xl">
                    <h2 className="text-2xl font-bold mb-6 text-blue-800 flex items-center gap-3">
                      <DollarSign className="w-6 h-6" />
                      Payment Terms
                    </h2>
                    <p className="text-blue-800 leading-relaxed">
                      {trip.payment_terms}
                    </p>
                  </motion.section>
                )}

                {/* Cancellation Policy */}
                {trip.cancellation_policy && (
                  <motion.section variants={itemVariants} className="bg-red-50 border border-red-200 rounded-2xl p-8 shadow-xl">
                    <h2 className="text-2xl font-bold mb-6 text-red-800 flex items-center gap-3">
                      <AlertTriangle className="w-6 h-6" />
                      Cancellation Policy
                    </h2>
                    <div className="space-y-3 text-red-800">
                      {typeof trip.cancellation_policy === 'object' && trip.cancellation_policy !== null ? (
                        Object.entries(trip.cancellation_policy).map(([key, value]) => (
                          <div key={key} className="flex justify-between items-center">
                            <span className="capitalize">{key.replace(/_/g, ' ')}:</span>
                            <span className="font-medium">{String(value)}</span>
                          </div>
                        ))
                      ) : (
                        <p>{String(trip.cancellation_policy)}</p>
                      )}
                    </div>
                  </motion.section>
                )}

                {/* Available Dates */}
                {trip.available_dates && trip.available_dates.length > 0 && (
                  <motion.section variants={itemVariants} className="bg-blue-50 border border-blue-200 rounded-2xl p-8 shadow-xl">
                    <h2 className="text-2xl font-bold mb-6 text-blue-800 flex items-center gap-3">
                      <Calendar className="w-6 h-6" />
                      Available Dates
                    </h2>
                    <div className="space-y-2">
                      {trip.available_dates.map((date, index) => (
                        <div key={index} className="bg-white p-3 rounded-lg flex items-center gap-2 shadow-sm">
                          <Calendar className="w-4 h-4 text-blue-600 flex-shrink-0" />
                          <span className="text-blue-800 text-sm">{date}</span>
                        </div>
                      ))}
                    </div>
                  </motion.section>
                )}

                {/* Booking CTA for Mobile */}
                <motion.section variants={itemVariants} className="lg:hidden bg-white rounded-2xl p-6 shadow-xl">
                  <div className="text-center">
                    <div className="text-2xl font-bold mb-2">₹{trip.price_per_person.toLocaleString()}</div>
                    <div className="text-gray-600 mb-4">per person</div>
                    
                    <Link href="/contact" className="block">
                      <Button size="lg" className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                        Book This Trip
                      </Button>
                    </Link>
                  </div>
                </motion.section>
              </div>

              {/* Right Column - Booking Sidebar */}
              <div className="lg:col-span-1 hidden lg:block">
                <motion.div variants={itemVariants} className="sticky top-24 space-y-6">
                  {/* Pricing Card */}
                  <div className="bg-white rounded-2xl p-6 shadow-xl">
                    <div className="text-center mb-6">
                      <div className="text-3xl font-bold mb-2">₹{trip.price_per_person.toLocaleString()}</div>
                      <div className="text-gray-600">per person</div>
                      {trip.commercial_price && trip.commercial_price !== trip.price_per_person && (
                        <div className="text-sm text-gray-500 line-through">₹{trip.commercial_price.toLocaleString()}</div>
                      )}
                    </div>

                    {/* Quick Info */}
                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Duration</span>
                        <span className="font-medium">{trip.days} Days, {trip.nights} Nights</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Age Range</span>
                        <div className="text-gray-600">
                          {trip.min_age && trip.max_age 
                            ? `${trip.min_age} - ${trip.max_age} years` 
                            : trip.min_age 
                              ? `${trip.min_age}+ years` 
                              : trip.max_age 
                                ? `Up to ${trip.max_age} years` 
                                : 'All ages'}
                        </div>
                      </div>
                      {trip.category && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Category</span>
                          <span className="font-medium flex items-center gap-1">
                            <Tag className="w-4 h-4" />
                            {trip.category}
                          </span>
                        </div>
                      )}
                    </div>

                    <Link href="/contact" className="block">
                      <Button size="lg" className="w-full mb-4 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                        Book This Trip
                      </Button>
                    </Link>

                    {/* Contact Options */}
                    <div className="space-y-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => window.open('tel:+917878005500', '_self')}
                      >
                        <Phone className="w-4 h-4 mr-2" />
                        Call: +91 78780 05500
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => window.open('http://wa.me/+917878005500?text=Hi%20i%20have%20enquiry%20about%20' + encodeURIComponent(trip.title) + '%20trip', '_blank')}
                      >
                        WhatsApp Inquiry
                      </Button>
                    </div>

                    <div className="text-center text-sm text-gray-500 mt-4">
                      <p>Contact us for group bookings and custom packages</p>
                    </div>
                  </div>

                  {/* Quick Features */}
                  <div className="bg-white rounded-2xl p-6 shadow-xl">
                    <h3 className="font-semibold text-gray-900 mb-4">Why Choose This Trip?</h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <Shield className="w-4 h-4 text-green-600" />
                        <span className="text-sm text-gray-700">Safety First</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Users className="w-4 h-4 text-blue-600" />
                        <span className="text-sm text-gray-700">Expert Guides</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm text-gray-700">All Inclusive</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone className="w-4 h-4 text-purple-600" />
                        <span className="text-sm text-gray-700">24/7 Support</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Related Trips */}
            {relatedTrips.length > 0 && (
              <motion.section variants={itemVariants} className="mt-16">
                <h2 className="text-3xl font-bold mb-8 text-gray-900">Related Trips</h2>
                <div className="grid md:grid-cols-3 gap-6">
                  {relatedTrips.map((relatedTrip) => (
                    <Link key={relatedTrip.id} href={`/trips/${relatedTrip.slug}`}>
                      <div className="bg-white rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-shadow">
                        <div className="relative h-48">
                          <Image
                            src={relatedTrip.featured_image_url || '/images/fallback-trip.jpg'}
                            alt={relatedTrip.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="p-4">
                          <h3 className="font-semibold text-gray-900 mb-2">{relatedTrip.title}</h3>
                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {relatedTrip.description || 'Discover amazing experiences'}
                          </p>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">{relatedTrip.days} Days, {relatedTrip.nights} Nights</span>
                            <span className="font-semibold text-primary-600">₹{relatedTrip.price_per_person.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </motion.section>
            )}
          </div>
        </motion.div>
      </div>
    </>
  );
}
