'use client'

import { useState, useMemo } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  Download, 
  Share2, 
  Heart,
  Filter,
  Grid3X3,
  List,
  Search
} from 'lucide-react'
import Button from '@/components/ui/Button'

interface PhotoItem {
  id: string
  src: string
  alt: string
  category: string
  location?: string
  day?: number
  description?: string
  photographer?: string
  tags: string[]
}

interface PhotoGalleryProps {
  photos: PhotoItem[]
  title: string
}

const CATEGORIES = [
  { id: 'all', name: 'All Photos', icon: Grid3X3 },
  { id: 'landscapes', name: 'Landscapes', icon: Grid3X3 },
  { id: 'activities', name: 'Activities', icon: Grid3X3 },
  { id: 'group', name: 'Group Photos', icon: Grid3X3 },
  { id: 'accommodation', name: 'Accommodation', icon: Grid3X3 },
  { id: 'food', name: 'Food & Culture', icon: Grid3X3 }
]

const LAYOUT_OPTIONS = [
  { id: 'masonry', name: 'Masonry', icon: Grid3X3 },
  { id: 'grid', name: 'Grid', icon: Grid3X3 },
  { id: 'list', name: 'List', icon: List }
]

export function PhotoGallery({ photos, title }: PhotoGalleryProps) {
  const [selectedPhoto, setSelectedPhoto] = useState<number | null>(null)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [layout, setLayout] = useState<'masonry' | 'grid' | 'list'>('masonry')
  const [searchTerm, setSearchTerm] = useState('')
  const [favorites, setFavorites] = useState<Set<string>>(new Set())

  // Filter photos based on category and search
  const filteredPhotos = useMemo(() => {
    let filtered = photos

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(photo => photo.category === selectedCategory)
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(photo => 
        photo.alt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        photo.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        photo.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ||
        photo.location?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return filtered
  }, [photos, selectedCategory, searchTerm])

  const openLightbox = (index: number) => {
    setSelectedPhoto(index)
  }

  const closeLightbox = () => {
    setSelectedPhoto(null)
  }

  const nextPhoto = () => {
    if (selectedPhoto !== null) {
      setSelectedPhoto((selectedPhoto + 1) % filteredPhotos.length)
    }
  }

  const prevPhoto = () => {
    if (selectedPhoto !== null) {
      setSelectedPhoto(selectedPhoto === 0 ? filteredPhotos.length - 1 : selectedPhoto - 1)
    }
  }

  const toggleFavorite = (photoId: string) => {
    const newFavorites = new Set(favorites)
    if (newFavorites.has(photoId)) {
      newFavorites.delete(photoId)
    } else {
      newFavorites.add(photoId)
    }
    setFavorites(newFavorites)
  }

  const downloadPhoto = (photo: PhotoItem) => {
    // In a real implementation, this would trigger a download
    console.log('Downloading photo:', photo.alt)
  }

  const sharePhoto = (photo: PhotoItem) => {
    // In a real implementation, this would open share dialog
    console.log('Sharing photo:', photo.alt)
  }

  return (
    <div className="bg-white rounded-2xl p-8 shadow-lg">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">{title} Gallery</h2>
        <p className="text-gray-600">Explore stunning photos from this amazing journey</p>
      </div>

      {/* Controls */}
      <div className="mb-8 space-y-4">
        {/* Search and Layout */}
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search photos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Layout:</span>
            {LAYOUT_OPTIONS.map((option) => (
              <button
                key={option.id}
                onClick={() => setLayout(option.id as any)}
                className={`p-2 rounded-lg transition-colors ${
                  layout === option.id
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                title={option.name}
              >
                <option.icon className="w-4 h-4" />
              </button>
            ))}
          </div>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-2">
          {CATEGORIES.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Results Count */}
        <div className="text-sm text-gray-600">
          Showing {filteredPhotos.length} of {photos.length} photos
        </div>
      </div>

      {/* Photo Grid */}
      <div className={`
        ${layout === 'masonry' ? 'columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-4' : ''}
        ${layout === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4' : ''}
        ${layout === 'list' ? 'space-y-4' : ''}
      `}>
        {filteredPhotos.map((photo, index) => (
          <motion.div
            key={photo.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            className={`
              group cursor-pointer relative overflow-hidden rounded-lg
              ${layout === 'masonry' ? 'mb-4 break-inside-avoid' : ''}
              ${layout === 'grid' ? 'aspect-square' : ''}
              ${layout === 'list' ? 'flex gap-4 p-4 bg-gray-50 rounded-lg' : ''}
            `}
            onClick={() => openLightbox(index)}
          >
            {layout === 'list' ? (
              <>
                <div className="w-32 h-32 relative rounded-lg overflow-hidden flex-shrink-0">
                  <Image
                    src={photo.src}
                    alt={photo.alt}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 mb-1">{photo.alt}</h3>
                  {photo.description && (
                    <p className="text-sm text-gray-600 mb-2">{photo.description}</p>
                  )}
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    {photo.location && <span>📍 {photo.location}</span>}
                    {photo.day && <span>Day {photo.day}</span>}
                    {photo.photographer && <span>📸 {photo.photographer}</span>}
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {photo.tags.slice(0, 3).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className={`relative ${layout === 'grid' ? 'aspect-square' : 'aspect-auto'}`}>
                  <Image
                    src={photo.src}
                    alt={photo.alt}
                    fill={layout === 'grid'}
                    width={layout === 'masonry' ? 400 : undefined}
                    height={layout === 'masonry' ? 300 : undefined}
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleFavorite(photo.id)
                        }}
                        className="p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors"
                      >
                        <Heart className={`w-4 h-4 ${favorites.has(photo.id) ? 'fill-red-500 text-red-500' : 'text-white'}`} />
                      </button>
                      <button className="p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors">
                        <ZoomIn className="w-4 h-4 text-white" />
                      </button>
                    </div>
                  </div>
                </div>
                
                {/* Photo Info */}
                <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/70 to-transparent text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h3 className="font-medium text-sm mb-1">{photo.alt}</h3>
                  <div className="flex items-center gap-2 text-xs">
                    {photo.location && <span>📍 {photo.location}</span>}
                    {photo.day && <span>Day {photo.day}</span>}
                  </div>
                </div>
              </>
            )}
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {filteredPhotos.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No photos found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria</p>
        </div>
      )}

      {/* Lightbox Modal */}
      <AnimatePresence>
        {selectedPhoto !== null && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
            onClick={closeLightbox}
          >
            {/* Controls */}
            <div className="absolute top-4 right-4 z-10 flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
                onClick={(e) => {
                  e.stopPropagation()
                  toggleFavorite(filteredPhotos[selectedPhoto].id)
                }}
              >
                <Heart className={`w-5 h-5 ${favorites.has(filteredPhotos[selectedPhoto].id) ? 'fill-red-500 text-red-500' : ''}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
                onClick={(e) => {
                  e.stopPropagation()
                  sharePhoto(filteredPhotos[selectedPhoto])
                }}
              >
                <Share2 className="w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
                onClick={(e) => {
                  e.stopPropagation()
                  downloadPhoto(filteredPhotos[selectedPhoto])
                }}
              >
                <Download className="w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
                onClick={closeLightbox}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            {/* Navigation */}
            {filteredPhotos.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20"
                  onClick={(e) => {
                    e.stopPropagation()
                    prevPhoto()
                  }}
                >
                  <ChevronLeft className="w-6 h-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20"
                  onClick={(e) => {
                    e.stopPropagation()
                    nextPhoto()
                  }}
                >
                  <ChevronRight className="w-6 h-6" />
                </Button>
              </>
            )}

            {/* Main Image */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="relative max-w-6xl max-h-[80vh] w-full h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <Image
                src={filteredPhotos[selectedPhoto].src}
                alt={filteredPhotos[selectedPhoto].alt}
                fill
                className="object-contain"
              />
            </motion.div>

            {/* Photo Info */}
            <div className="absolute bottom-4 left-4 right-4 bg-black/50 text-white p-4 rounded-lg backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium mb-1">{filteredPhotos[selectedPhoto].alt}</h3>
                  {filteredPhotos[selectedPhoto].description && (
                    <p className="text-sm text-white/80 mb-2">{filteredPhotos[selectedPhoto].description}</p>
                  )}
                  <div className="flex items-center gap-4 text-sm text-white/70">
                    {filteredPhotos[selectedPhoto].location && (
                      <span>📍 {filteredPhotos[selectedPhoto].location}</span>
                    )}
                    {filteredPhotos[selectedPhoto].day && (
                      <span>Day {filteredPhotos[selectedPhoto].day}</span>
                    )}
                    {filteredPhotos[selectedPhoto].photographer && (
                      <span>📸 {filteredPhotos[selectedPhoto].photographer}</span>
                    )}
                  </div>
                </div>
                <div className="text-sm text-white/70">
                  {selectedPhoto + 1} / {filteredPhotos.length}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
