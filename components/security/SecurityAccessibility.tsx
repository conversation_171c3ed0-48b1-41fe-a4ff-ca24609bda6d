'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Shield, 
  Eye, 
  EyeOff, 
  Volume2, 
  VolumeX, 
  Type, 
  Contrast,
  MousePointer,
  Keyboard,
  AlertTriangle,
  Lock,
  CheckCircle
} from 'lucide-react'

// Security Headers Component
export function SecurityHeaders() {
  useEffect(() => {
    // Only add security headers if they don't already exist
    // These are backup client-side headers in case the Next.js config headers aren't working
    
    // Check if we're in a browser environment
    if (typeof window === 'undefined') return;

    // Content Security Policy - only add if not already set by Next.js
    if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
      const meta = document.createElement('meta');
      meta.httpEquiv = 'Content-Security-Policy';
      meta.content = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.positive7.in https://*.supabase.co https://soaoagcuubtzojytoati.supabase.co wss://soaoagcuubtzojytoati.supabase.co;";
      document.head.appendChild(meta);
    }

    // X-Frame-Options - only add if not already set by Next.js
    if (!document.querySelector('meta[http-equiv="X-Frame-Options"]')) {
      const frameOptions = document.createElement('meta');
      frameOptions.httpEquiv = 'X-Frame-Options';
      frameOptions.content = 'DENY';
      document.head.appendChild(frameOptions);
    }

    // X-Content-Type-Options - only add if not already set by Next.js
    if (!document.querySelector('meta[http-equiv="X-Content-Type-Options"]')) {
      const contentType = document.createElement('meta');
      contentType.httpEquiv = 'X-Content-Type-Options';
      contentType.content = 'nosniff';
      document.head.appendChild(contentType);
    }

    // Clean up function - only remove elements we added
    return () => {
      // We don't need to remove anything since we're only adding if not present
    };
  }, []);

  return null;
}

// Input Sanitization Hook
export function useSanitizedInput(initialValue = '') {
  const [value, setValue] = useState(initialValue)
  const [sanitizedValue, setSanitizedValue] = useState(initialValue)

  const sanitize = useCallback((input: string) => {
    // Remove potentially dangerous characters and scripts
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .replace(/[<>]/g, '')
      .trim()
  }, [])

  useEffect(() => {
    setSanitizedValue(sanitize(value))
  }, [value, sanitize])

  return {
    value,
    sanitizedValue,
    setValue,
    isValid: value === sanitizedValue
  }
}

// Accessibility Toolbar Component
export function AccessibilityToolbar() {
  const [isOpen, setIsOpen] = useState(false)
  const [settings, setSettings] = useState({
    fontSize: 100,
    contrast: false,
    screenReader: false,
    keyboardNav: false,
    reducedMotion: false
  })

  useEffect(() => {
    // Apply accessibility settings
    const root = document.documentElement

    // Font size
    root.style.fontSize = `${settings.fontSize}%`

    // High contrast
    if (settings.contrast) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }

    // Reduced motion
    if (settings.reducedMotion) {
      root.classList.add('reduce-motion')
    } else {
      root.classList.remove('reduce-motion')
    }

    // Keyboard navigation
    if (settings.keyboardNav) {
      root.classList.add('keyboard-nav')
    } else {
      root.classList.remove('keyboard-nav')
    }
  }, [settings])

  const updateSetting = (key: keyof typeof settings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  return (
    <>
      {/* Accessibility Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed left-4 top-1/2 -translate-y-1/2 z-50 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label="Open accessibility toolbar"
        aria-expanded={isOpen}
      >
        <Eye className="w-5 h-5" />
      </button>

      {/* Accessibility Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: -300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -300, opacity: 0 }}
            className="fixed left-0 top-0 h-full w-80 bg-white shadow-2xl z-40 overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Accessibility</h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                  aria-label="Close accessibility toolbar"
                >
                  <EyeOff className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Font Size */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Type className="w-4 h-4 inline mr-2" />
                    Font Size: {settings.fontSize}%
                  </label>
                  <input
                    type="range"
                    min="75"
                    max="150"
                    step="25"
                    value={settings.fontSize}
                    onChange={(e) => updateSetting('fontSize', parseInt(e.target.value))}
                    className="w-full"
                    aria-label="Adjust font size"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>75%</span>
                    <span>100%</span>
                    <span>125%</span>
                    <span>150%</span>
                  </div>
                </div>

                {/* High Contrast */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center text-sm font-medium text-gray-700">
                    <Contrast className="w-4 h-4 mr-2" />
                    High Contrast
                  </label>
                  <button
                    onClick={() => updateSetting('contrast', !settings.contrast)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.contrast ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                    aria-pressed={settings.contrast}
                    aria-label="Toggle high contrast mode"
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.contrast ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* Screen Reader */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center text-sm font-medium text-gray-700">
                    <Volume2 className="w-4 h-4 mr-2" />
                    Screen Reader
                  </label>
                  <button
                    onClick={() => updateSetting('screenReader', !settings.screenReader)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.screenReader ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                    aria-pressed={settings.screenReader}
                    aria-label="Toggle screen reader support"
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.screenReader ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* Keyboard Navigation */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center text-sm font-medium text-gray-700">
                    <Keyboard className="w-4 h-4 mr-2" />
                    Keyboard Navigation
                  </label>
                  <button
                    onClick={() => updateSetting('keyboardNav', !settings.keyboardNav)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.keyboardNav ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                    aria-pressed={settings.keyboardNav}
                    aria-label="Toggle keyboard navigation highlights"
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.keyboardNav ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* Reduced Motion */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center text-sm font-medium text-gray-700">
                    <MousePointer className="w-4 h-4 mr-2" />
                    Reduce Motion
                  </label>
                  <button
                    onClick={() => updateSetting('reducedMotion', !settings.reducedMotion)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.reducedMotion ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                    aria-pressed={settings.reducedMotion}
                    aria-label="Toggle reduced motion"
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.reducedMotion ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* Reset Button */}
                <button
                  onClick={() => setSettings({
                    fontSize: 100,
                    contrast: false,
                    screenReader: false,
                    keyboardNav: false,
                    reducedMotion: false
                  })}
                  className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg transition-colors"
                >
                  Reset to Default
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-30"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}
    </>
  )
}

// Secure Form Component
interface SecureFormProps {
  onSubmit: (data: any) => void
  children: React.ReactNode
  className?: string
}

export function SecureForm({ onSubmit, children, className = '' }: SecureFormProps) {
  const formRef = useRef<HTMLFormElement>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [csrfToken, setCsrfToken] = useState('')

  useEffect(() => {
    // Generate CSRF token
    const token = Math.random().toString(36).substring(2, 15) + 
                  Math.random().toString(36).substring(2, 15)
    setCsrfToken(token)
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (isSubmitting) return

    setIsSubmitting(true)

    try {
      const formData = new FormData(formRef.current!)
      const data = Object.fromEntries(formData.entries())
      
      // Add CSRF token
      data.csrfToken = csrfToken

      // Validate and sanitize data
      const sanitizedData = Object.keys(data).reduce((acc, key) => {
        if (typeof data[key] === 'string') {
          acc[key] = (data[key] as string)
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .trim()
        } else {
          acc[key] = data[key]
        }
        return acc
      }, {} as any)

      await onSubmit(sanitizedData)
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form
      ref={formRef}
      onSubmit={handleSubmit}
      className={className}
      noValidate
      autoComplete="on"
    >
      <input type="hidden" name="csrfToken" value={csrfToken} />
      {children}
    </form>
  )
}

// Security Status Component
export function SecurityStatus() {
  const [securityChecks, setSecurityChecks] = useState({
    https: false,
    csp: false,
    xframe: false,
    nosniff: false,
    hsts: false
  })

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') return;

    // Check HTTPS
    const isHttps = window.location.protocol === 'https:' || 
                    window.location.hostname === 'localhost' || 
                    window.location.hostname === '127.0.0.1';
    
    setSecurityChecks(prev => ({
      ...prev,
      https: isHttps
    }))

    // Check CSP
    const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    const cspHeader = !!cspMeta;
    
    setSecurityChecks(prev => ({
      ...prev,
      csp: cspHeader
    }))

    // Check X-Frame-Options
    const frameMeta = document.querySelector('meta[http-equiv="X-Frame-Options"]');
    setSecurityChecks(prev => ({
      ...prev,
      xframe: !!frameMeta
    }))

    // Check X-Content-Type-Options
    const nosniffMeta = document.querySelector('meta[http-equiv="X-Content-Type-Options"]');
    setSecurityChecks(prev => ({
      ...prev,
      nosniff: !!nosniffMeta
    }))

    // For HSTS, we can't directly check if the header is set from client-side
    // For development environments, we'll consider it secure if running on localhost
    const isLocalDev = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1';
    
    setSecurityChecks(prev => ({
      ...prev,
      hsts: isLocalDev || window.location.protocol === 'https:'
    }))
  }, [])

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const allSecure = Object.values(securityChecks).every(Boolean)

  return (
    <div className="fixed bottom-4 left-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-xs">
      <div className="flex items-center gap-2 mb-3">
        <Shield className={`w-5 h-5 ${allSecure ? 'text-green-600' : 'text-yellow-600'}`} />
        <h3 className="font-semibold text-gray-900">Security Status</h3>
      </div>
      
      <div className="space-y-2 text-sm">
        {Object.entries(securityChecks).map(([check, passed]) => (
          <div key={check} className="flex items-center justify-between">
            <span className="text-gray-600 capitalize">{check.replace(/([A-Z])/g, ' $1')}</span>
            {passed ? (
              <CheckCircle className="w-4 h-4 text-green-600" />
            ) : (
              <AlertTriangle className="w-4 h-4 text-red-600" />
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

// ARIA Live Region for Screen Readers
export function AriaLiveRegion() {
  const [message, setMessage] = useState('')
  const [politeness, setPoliteness] = useState<'polite' | 'assertive'>('polite')

  useEffect(() => {
    // Global function to announce messages to screen readers
    (window as any).announceToScreenReader = (msg: string, urgent = false) => {
      setPoliteness(urgent ? 'assertive' : 'polite')
      setMessage(msg)
      
      // Clear message after announcement
      setTimeout(() => setMessage(''), 1000)
    }

    return () => {
      delete (window as any).announceToScreenReader
    }
  }, [])

  return (
    <div
      aria-live={politeness}
      aria-atomic="true"
      className="sr-only"
    >
      {message}
    </div>
  )
}

// Skip to Content Link
export function SkipToContent() {
  return (
    <a
      href="#main-content"
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
    >
      Skip to main content
    </a>
  )
}

// Focus Management Hook
export function useFocusManagement() {
  const focusRef = useRef<HTMLElement>(null)

  const setFocus = useCallback(() => {
    if (focusRef.current) {
      focusRef.current.focus()
    }
  }, [])

  const trapFocus = useCallback((e: KeyboardEvent) => {
    if (e.key !== 'Tab') return

    const focusableElements = focusRef.current?.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )

    if (!focusableElements || focusableElements.length === 0) return

    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        e.preventDefault()
        lastElement.focus()
      }
    } else {
      if (document.activeElement === lastElement) {
        e.preventDefault()
        firstElement.focus()
      }
    }
  }, [])

  return { focusRef, setFocus, trapFocus }
}
