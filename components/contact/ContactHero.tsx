'use client'

import { motion } from 'framer-motion'
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  MessageCircle,
  Users,
  Star,
  ArrowRight
} from 'lucide-react'
import Button from '@/components/ui/Button'

export function ContactHero() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <section className="relative py-20 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-green-600/10" />

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-7xl mx-auto px-4 relative"
      >
        <div className="text-center mb-16">
          <motion.div variants={itemVariants}>
            <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <MessageCircle className="w-4 h-4" />
              We're Here to Help
            </div>
          </motion.div>

          <motion.h1 variants={itemVariants} className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Let's Plan Your
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600"> Perfect Trip</span>
          </motion.h1>

          <motion.p variants={itemVariants} className="text-xl text-gray-700 max-w-3xl mx-auto mb-8 leading-relaxed">
            Ready to create unforgettable educational experiences? Our expert team is here to help you plan
            the perfect learning adventure for your students.
          </motion.p>

          <motion.div variants={itemVariants} className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-green-600"
              onClick={() => window.open('tel:+917878005500', '_self')}
            >
              <Phone className="w-5 h-5 mr-2" />
              Call Now: +91 78780 05500
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => window.open('mailto:<EMAIL>', '_self')}
            >
              <Mail className="w-5 h-5 mr-2" />
              Send Email
            </Button>
          </motion.div>
        </div>

        {/* Quick Contact Cards */}
        <motion.div variants={itemVariants} className="grid md:grid-cols-4 gap-6">
          <div className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Phone className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Call Us</h3>
            <p className="text-gray-600 text-sm mb-3">Speak directly with our experts</p>
            <a
              href="tel:+917878005500"
              className="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center justify-center gap-1"
            >
              +91 78780 05500
              <ArrowRight className="w-3 h-3" />
            </a>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Email Us</h3>
            <p className="text-gray-600 text-sm mb-3">Get detailed information</p>
            <a
              href="mailto:<EMAIL>"
              className="text-green-600 hover:text-green-700 font-medium text-sm flex items-center justify-center gap-1"
            >
              <EMAIL>
              <ArrowRight className="w-3 h-3" />
            </a>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Visit Office</h3>
            <p className="text-gray-600 text-sm mb-3">Meet us in person</p>
            <span className="text-purple-600 font-medium text-sm flex items-center justify-center gap-1">
              Ahmedabad, Gujarat
              <ArrowRight className="w-3 h-3" />
            </span>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Office Hours</h3>
            <p className="text-gray-600 text-sm mb-3">We're available</p>
            <span className="text-orange-600 font-medium text-sm">
              9:00 AM - 8:00 PM
            </span>
          </div>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div variants={itemVariants} className="mt-16 text-center">
          <div className="inline-flex items-center gap-8 bg-white/80 backdrop-blur-sm rounded-2xl px-8 py-4 shadow-lg">
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              <span className="text-sm text-gray-700">
                <span className="font-semibold">50,000+</span> Students Served
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-500 fill-current" />
              <span className="text-sm text-gray-700">
                <span className="font-semibold">4.8/5</span> Rating
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-green-600" />
              <span className="text-sm text-gray-700">
                <span className="font-semibold">24/7</span> Support
              </span>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  )
}
