'use client'

import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import dynamic from 'next/dynamic'

// Simple intersection observer hook implementation
function useIntersectionObserver(threshold = 0.1) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
      },
      { threshold }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [threshold])

  return { ref, isIntersecting }
}

// Lazy load heavy components
const LazyChart = dynamic(() => import('recharts').then(mod => mod.BarChart), {
  loading: () => <div className="h-64 bg-gray-200 animate-pulse rounded-lg" />,
  ssr: false
})

// Optimized Image Component with lazy loading and blur placeholder
interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  sizes?: string
  fill?: boolean
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  fill = false
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(false)

  const handleLoad = useCallback(() => {
    setIsLoading(false)
  }, [])

  const handleError = useCallback(() => {
    setError(true)
    setIsLoading(false)
  }, [])

  if (error) {
    return (
      <div className={`bg-gray-200 flex items-center justify-center ${className}`}>
        <span className="text-gray-500 text-sm">Image unavailable</span>
      </div>
    )
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <Image
        src={src}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        sizes={sizes}
        priority={priority}
        quality={85}
        placeholder="blur"
        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
        onLoad={handleLoad}
        onError={handleError}
        className={`transition-opacity duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        } ${fill ? 'object-cover' : ''}`}
      />
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
    </div>
  )
}

// Intersection Observer Hook for lazy loading
export function useLazyLoad(threshold = 0.1) {
  const { ref, isIntersecting } = useIntersectionObserver(threshold)
  const [inView, setInView] = useState(false)

  useEffect(() => {
    if (isIntersecting && !inView) {
      setInView(true) // Once in view, stay in view (triggerOnce behavior)
    }
  }, [isIntersecting, inView])

  return { ref, inView }
}

// Virtual Scrolling Component for large lists
interface VirtualScrollProps {
  items: any[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: any, index: number) => React.ReactNode
  overscan?: number
}

export function VirtualScroll({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5
}: VirtualScrollProps) {
  const [scrollTop, setScrollTop] = useState(0)

  const visibleStart = Math.floor(scrollTop / itemHeight)
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight),
    items.length - 1
  )

  const paddingTop = visibleStart * itemHeight
  const paddingBottom = (items.length - visibleEnd - 1) * itemHeight

  const visibleItems = useMemo(() => {
    return items.slice(
      Math.max(0, visibleStart - overscan),
      Math.min(items.length, visibleEnd + 1 + overscan)
    )
  }, [items, visibleStart, visibleEnd, overscan])

  return (
    <div
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      <div style={{ paddingTop, paddingBottom }}>
        {visibleItems.map((item, index) => (
          <div key={visibleStart - overscan + index} style={{ height: itemHeight }}>
            {renderItem(item, visibleStart - overscan + index)}
          </div>
        ))}
      </div>
    </div>
  )
}

// Debounced Search Hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Memoized Search Component
interface SearchResultsProps {
  query: string
  items: any[]
  searchFields: string[]
  renderItem: (item: any) => React.ReactNode
}

export function MemoizedSearchResults({
  query,
  items,
  searchFields,
  renderItem
}: SearchResultsProps) {
  const debouncedQuery = useDebounce(query, 300)

  const filteredItems = useMemo(() => {
    if (!debouncedQuery.trim()) return items

    return items.filter(item =>
      searchFields.some(field =>
        item[field]?.toLowerCase().includes(debouncedQuery.toLowerCase())
      )
    )
  }, [items, debouncedQuery, searchFields])

  return (
    <div>
      {filteredItems.map((item, index) => (
        <div key={item.id || index}>
          {renderItem(item)}
        </div>
      ))}
    </div>
  )
}

// Lazy Loading Section Component
interface LazyLoadSectionProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  threshold?: number
  className?: string
}

export function LazyLoadSection({
  children,
  fallback = <div className="h-64 bg-gray-200 animate-pulse rounded-lg" />,
  threshold = 0.1,
  className = ''
}: LazyLoadSectionProps) {
  const { ref, inView } = useLazyLoad(threshold)

  return (
    <div ref={ref} className={className}>
      {inView ? children : fallback}
    </div>
  )
}

// Performance Monitoring Hook
export function usePerformanceMonitoring() {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0
  })
  
  // Define a function to load web-vitals that can be conditionally called
  const loadWebVitals = useCallback(async () => {
    try {
      const webVitals = await import('web-vitals');
      webVitals.onCLS(console.log);
      webVitals.onFCP(console.log);
      webVitals.onLCP(console.log);
      webVitals.onTTFB(console.log);
      if (webVitals.onINP) {
        webVitals.onINP(console.log);
      }
    } catch (error) {
      // web-vitals not available, skip
      console.error('Failed to load web-vitals:', error);
    }
  }, []);

  useEffect(() => {
    // Measure page load time
    let loadTime = 0;
    if (typeof performance !== 'undefined' && performance.timing) {
      loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    }

    // Measure render time
    const renderStart = performance.now();

    // Memory usage (if available)
    let memoryUsage = 0;
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      memoryUsage = (performance as any).memory.usedJSHeapSize || 0;
    }

    setTimeout(() => {
      const renderTime = performance.now() - renderStart;
      setMetrics({
        loadTime,
        renderTime,
        memoryUsage
      });
    }, 0);

    // Report Core Web Vitals
    loadWebVitals();
  }, [loadWebVitals]);

  return metrics;
}

// Optimized Animation Component
interface OptimizedAnimationProps {
  children: React.ReactNode
  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'scale'
  duration?: number
  delay?: number
  threshold?: number
}

export function OptimizedAnimation({
  children,
  animation = 'fadeIn',
  duration = 0.5,
  delay = 0,
  threshold = 0.1
}: OptimizedAnimationProps) {
  const { ref, inView } = useLazyLoad(threshold)

  const animations = {
    fadeIn: {
      initial: { opacity: 0 },
      animate: { opacity: 1 }
    },
    slideUp: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 }
    },
    slideLeft: {
      initial: { opacity: 0, x: 20 },
      animate: { opacity: 1, x: 0 }
    },
    scale: {
      initial: { opacity: 0, scale: 0.9 },
      animate: { opacity: 1, scale: 1 }
    }
  }

  return (
    <div ref={ref}>
      <AnimatePresence>
        {inView && (
          <motion.div
            initial={animations[animation].initial}
            animate={animations[animation].animate}
            transition={{ duration, delay }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Image Preloader Hook
export function useImagePreloader(imageUrls: string[]) {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set())

  useEffect(() => {
    const preloadImages = async () => {
      const promises = imageUrls.map(url => {
        return new Promise<string>((resolve, reject) => {
          const img = new window.Image()
          img.onload = () => resolve(url)
          img.onerror = reject
          img.src = url
        })
      })

      try {
        const loaded = await Promise.allSettled(promises)
        const successful = loaded
          .filter(result => result.status === 'fulfilled')
          .map(result => (result as PromiseFulfilledResult<string>).value)

        setLoadedImages(new Set(successful))
      } catch (error) {
        console.error('Error preloading images:', error)
      }
    }

    if (imageUrls.length > 0) {
      preloadImages()
    }
  }, [imageUrls])

  return loadedImages
}

// Bundle Size Analyzer 
export function BundleAnalyzer() {
  const [bundleInfo, setBundleInfo] = useState<any>(null)
  const [isDev, setIsDev] = useState(false)

  useEffect(() => {
    // Check if we're in development mode
    setIsDev(process.env.NODE_ENV === 'development')
    
    // This would integrate with webpack-bundle-analyzer in development
    if (typeof window !== 'undefined' && (window as any).__BUNDLE_ANALYZER__) {
      setBundleInfo((window as any).__BUNDLE_ANALYZER__)
    }
  }, [])

  if (!isDev || !bundleInfo) return null

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs">
      <h4 className="font-bold mb-2">Bundle Info</h4>
      <div>Total Size: {bundleInfo.totalSize}</div>
      <div>Chunks: {bundleInfo.chunks}</div>
      <div>Modules: {bundleInfo.modules}</div>
    </div>
  )
}

// Performance Budget Component
export function PerformanceBudget() {
  const metrics = usePerformanceMonitoring()
  const [warnings, setWarnings] = useState<string[]>([])
  const [isDev, setIsDev] = useState(false)

  useEffect(() => {
    setIsDev(process.env.NODE_ENV === 'development')
  }, [])

  useEffect(() => {
    const newWarnings: string[] = []

    if (metrics.loadTime > 3000) {
      newWarnings.push('Page load time exceeds 3 seconds')
    }

    if (metrics.memoryUsage > 50 * 1024 * 1024) { // 50MB
      newWarnings.push('Memory usage exceeds 50MB')
    }

    setWarnings(newWarnings)
  }, [metrics])

  if (!isDev || warnings.length === 0) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg text-sm max-w-xs">
      <h4 className="font-bold mb-2">Performance Warnings</h4>
      <ul className="space-y-1">
        {warnings.map((warning, index) => (
          <li key={index}>• {warning}</li>
        ))}
      </ul>
    </div>
  )
}
