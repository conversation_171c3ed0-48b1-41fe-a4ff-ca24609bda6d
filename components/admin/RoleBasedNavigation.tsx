'use client';

import Link from 'next/link';
import { useAuth } from '@/lib/hooks/useAuth';
import { 
  Users, 
  MapPin, 
  FileText, 
  MessageSquare, 
  Camera, 
  BarChart3,
  Settings,
  Shield
} from 'lucide-react';

interface NavItem {
  href: string;
  label: string;
  icon: React.ComponentType<any>;
  resource: string;
  action: string;
  description?: string;
}

const allNavItems: NavItem[] = [
  {
    href: '/admin/trips',
    label: 'Trips',
    icon: MapPin,
    resource: 'trips',
    action: 'read',
    description: 'Manage travel packages and destinations'
  },
  {
    href: '/admin/blog',
    label: 'Blog',
    icon: FileText,
    resource: 'blog',
    action: 'read',
    description: 'Manage blog posts and content'
  },
  {
    href: '/admin/inquiries',
    label: 'Inquiries',
    icon: MessageSquare,
    resource: 'inquiries',
    action: 'read',
    description: 'Handle customer inquiries and support'
  },
  {
    href: '/admin/trips-photos',
    label: 'Trip Photos',
    icon: Camera,
    resource: 'trip_photos',
    action: 'read',
    description: 'Manage trip photo galleries'
  },
  {
    href: '/admin/analytics',
    label: 'Analytics',
    icon: BarChart3,
    resource: 'analytics',
    action: 'read',
    description: 'View website analytics and reports'
  },
  {
    href: '/admin/users',
    label: 'Admin Users',
    icon: Users,
    resource: 'users',
    action: 'read',
    description: 'Manage admin users and permissions'
  },
  {
    href: '/admin/roles',
    label: 'Roles & Permissions',
    icon: Shield,
    resource: 'roles',
    action: 'read',
    description: 'Manage user roles and permissions'
  }
];

export default function RoleBasedNavigation() {
  const { hasPermission, adminUser } = useAuth();

  if (!adminUser) {
    return null;
  }

  const allowedNavItems = allNavItems.filter(item => 
    hasPermission(item.resource, item.action)
  );

  return (
    <nav className="space-y-2">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">
          Welcome, {adminUser.full_name || adminUser.username || 'Admin'}
        </h2>
        <div className="flex flex-wrap gap-1">
          {adminUser.roles.map(role => (
            <span
              key={role.name}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {role.name.replace('_', ' ').toUpperCase()}
            </span>
          ))}
        </div>
      </div>

      <div className="space-y-1">
        {allowedNavItems.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.href}
              href={item.href as any}
              className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-colors"
            >
              <Icon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
              <div className="flex-1">
                <div className="font-medium">{item.label}</div>
                {item.description && (
                  <div className="text-xs text-gray-500 mt-0.5">
                    {item.description}
                  </div>
                )}
              </div>
            </Link>
          );
        })}
      </div>

      {allowedNavItems.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Shield className="mx-auto h-12 w-12 text-gray-300 mb-4" />
          <p className="text-sm">No accessible sections found.</p>
          <p className="text-xs mt-1">Contact your administrator for access.</p>
        </div>
      )}
    </nav>
  );
}
