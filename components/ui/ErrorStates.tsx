'use client'

import { Al<PERSON><PERSON>riangle, RefreshCw, Home, Mail, Wifi, Server, Search, FileX } from 'lucide-react'
import Button from './Button'

// Generic Error Component
export function ErrorState({ 
  title = "Something went wrong",
  message = "An unexpected error occurred. Please try again.",
  onRetry,
  showHomeButton = true,
  showContactButton = false,
  icon: Icon = AlertTriangle
}: {
  title?: string
  message?: string
  onRetry?: () => void
  showHomeButton?: boolean
  showContactButton?: boolean
  icon?: React.ComponentType<{ className?: string }>
}) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <Icon className="w-8 h-8 text-red-600" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-4">{title}</h3>
      <p className="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">{message}</p>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        {onRetry && (
          <Button onClick={onRetry} className="bg-gradient-to-r from-blue-600 to-green-600">
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        )}
        
        {showHomeButton && (
          <Button
            variant="outline"
            onClick={() => window.location.href = '/'}
          >
            <Home className="w-4 h-4 mr-2" />
            Go Home
          </Button>
        )}
        
        {showContactButton && (
          <Button
            variant="outline"
            onClick={() => window.location.href = '/contact'}
          >
            <Mail className="w-4 h-4 mr-2" />
            Contact Support
          </Button>
        )}
      </div>
    </div>
  )
}

// Network Error
export function NetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorState
      title="Connection Error"
      message="Unable to connect to our servers. Please check your internet connection and try again."
      onRetry={onRetry}
      icon={Wifi}
      showContactButton={true}
    />
  )
}

// Server Error
export function ServerError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorState
      title="Server Error"
      message="Our servers are experiencing some issues. Please try again in a few moments."
      onRetry={onRetry}
      icon={Server}
      showContactButton={true}
    />
  )
}

// 404 Not Found Error
export function NotFoundError({ 
  title = "Page Not Found",
  message = "The page you're looking for doesn't exist or has been moved.",
  showHomeButton = true 
}: { 
  title?: string
  message?: string
  showHomeButton?: boolean 
}) {
  return (
    <div className="text-center py-12">
      <div className="text-6xl font-bold text-gray-300 mb-4">404</div>
      <h3 className="text-2xl font-semibold text-gray-900 mb-4">{title}</h3>
      <p className="text-gray-600 mb-8 max-w-md mx-auto">{message}</p>
      {showHomeButton && (
        <Button
          onClick={() => window.location.href = '/'}
          className="bg-gradient-to-r from-blue-600 to-green-600"
        >
          <Home className="w-4 h-4 mr-2" />
          Go Home
        </Button>
      )}
    </div>
  )
}

// No Results Found
export function NoResultsError({ 
  title = "No Results Found",
  message = "We couldn't find any results matching your criteria. Try adjusting your search or filters.",
  onClearFilters,
  showSearchSuggestions = true
}: {
  title?: string
  message?: string
  onClearFilters?: () => void
  showSearchSuggestions?: boolean
}) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <Search className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-4">{title}</h3>
      <p className="text-gray-600 mb-8 max-w-md mx-auto">{message}</p>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        {onClearFilters && (
          <Button onClick={onClearFilters} variant="outline">
            Clear Filters
          </Button>
        )}
        
        <Button
          onClick={() => window.location.href = '/trips'}
          className="bg-gradient-to-r from-blue-600 to-green-600"
        >
          Browse All Trips
        </Button>
      </div>
      
      {showSearchSuggestions && (
        <div className="mt-8 text-left max-w-md mx-auto">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Popular Destinations:</h4>
          <div className="flex flex-wrap gap-2">
            {['Manali', 'Rishikesh', 'Dharamshala', 'Goa', 'Rajasthan'].map((destination) => (
              <button
                key={destination}
                onClick={() => window.location.href = `/search?q=${destination}`}
                className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full hover:bg-blue-200 transition-colors"
              >
                {destination}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Empty State
export function EmptyState({
  title = "Nothing here yet",
  message = "This section is empty. Check back later for updates.",
  actionLabel,
  onAction,
  icon: Icon = FileX
}: {
  title?: string
  message?: string
  actionLabel?: string
  onAction?: () => void
  icon?: React.ComponentType<{ className?: string }>
}) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <Icon className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-4">{title}</h3>
      <p className="text-gray-600 mb-8 max-w-md mx-auto">{message}</p>
      
      {actionLabel && onAction && (
        <Button
          onClick={onAction}
          className="bg-gradient-to-r from-blue-600 to-green-600"
        >
          {actionLabel}
        </Button>
      )}
    </div>
  )
}

// Form Error State
export function FormError({ 
  message = "Please check the form and try again.",
  onDismiss 
}: {
  message?: string
  onDismiss?: () => void
}) {
  return (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-start gap-3">
      <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
      <div className="flex-1">
        <div className="font-medium text-red-900">Error</div>
        <div className="text-sm text-red-700">{message}</div>
      </div>
      {onDismiss && (
        <button
          onClick={onDismiss}
          className="text-red-400 hover:text-red-600 transition-colors"
        >
          ×
        </button>
      )}
    </div>
  )
}

// Success State
export function SuccessState({
  title = "Success!",
  message = "Your action was completed successfully.",
  actionLabel,
  onAction
}: {
  title?: string
  message?: string
  actionLabel?: string
  onAction?: () => void
}) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-4">{title}</h3>
      <p className="text-gray-600 mb-8 max-w-md mx-auto">{message}</p>
      
      {actionLabel && onAction && (
        <Button
          onClick={onAction}
          className="bg-gradient-to-r from-blue-600 to-green-600"
        >
          {actionLabel}
        </Button>
      )}
    </div>
  )
}

// Maintenance Mode
export function MaintenanceMode() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        <div className="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-10 h-10 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          We'll be back soon!
        </h1>
        
        <p className="text-gray-600 mb-8 leading-relaxed">
          We're currently performing scheduled maintenance to improve your experience. 
          Please check back in a few minutes.
        </p>
        
        <div className="space-y-4">
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="w-full"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh Page
          </Button>
          
          <div className="text-sm text-gray-500">
            For urgent matters, contact us at{' '}
            <a href="tel:+917878005500" className="text-blue-600 hover:text-blue-700">
              +91 78780 05500
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
