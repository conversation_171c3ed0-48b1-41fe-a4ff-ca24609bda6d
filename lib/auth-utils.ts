import { cookies } from 'next/headers';
import { createServerSupabase } from './supabase-server';

export async function getAdminUser() {
  try {
    // Get the session token from the cookie
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('admin_session')?.value;

    if (!sessionToken) {
      return null;
    }

    // Connect to Supabase
    const supabase = createServerSupabase();

    // Find the session
    const { data: session, error: sessionError } = await supabase
      .from('admin_sessions')
      .select('id, user_id, expires_at')
      .eq('token', sessionToken)
      .single();

    if (sessionError || !session) {
      return null;
    }

    // Check if session is expired
    const now = new Date();
    const expiresAt = new Date(session.expires_at);
    
    if (now > expiresAt) {
      return null;
    }

    // Get the user
    const { data: user, error: userError } = await supabase
      .from('admin_users')
      .select('id, email, username')
      .eq('id', session.user_id)
      .single();

    if (userError || !user) {
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error getting admin user:', error);
    return null;
  }
} 