import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import type { Database } from '@/types/supabase'

// Server-side auth client for API routes and server components
export async function createServerSupabaseAuth() {
  const cookieStore = await cookies()

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          // Handle both default Supabase cookie names and our custom ones
          if (name === 'sb-access-token') {
            return cookieStore.get('sb-access-token')?.value || cookieStore.get('supabase-auth-token')?.value
          }
          if (name === 'sb-refresh-token') {
            return cookieStore.get('sb-refresh-token')?.value || cookieStore.get('supabase-auth-token-refresh')?.value
          }
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // The `delete` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}

// Middleware auth client
export function createMiddlewareSupabaseAuth(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          // Handle both default Supabase cookie names and our custom ones
          if (name === 'sb-access-token') {
            return request.cookies.get('sb-access-token')?.value || request.cookies.get('supabase-auth-token')?.value
          }
          if (name === 'sb-refresh-token') {
            return request.cookies.get('sb-refresh-token')?.value || request.cookies.get('supabase-auth-token-refresh')?.value
          }
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  return { supabase, response }
}

// Admin service client (for admin operations)
export function createAdminSupabaseClient() {
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

// Types for admin roles and permissions
export interface AdminRole {
  id: string
  name: string
  description: string | null
  permissions: Record<string, string[]>
  created_at: string
  updated_at: string
}

export interface AdminProfile {
  id: string
  username: string | null
  full_name: string | null
  avatar_url: string | null
  is_active: boolean
  last_login_at: string | null
  created_at: string
  updated_at: string
}

export interface AdminUserWithRoles extends AdminProfile {
  roles: AdminRole[]
}

// Permission checking utilities
export function hasPermission(
  userRoles: AdminRole[],
  resource: string,
  action: string
): boolean {
  return userRoles.some(role => {
    const resourcePermissions = role.permissions[resource]
    return resourcePermissions && resourcePermissions.includes(action)
  })
}

export function hasAnyRole(userRoles: AdminRole[], roleNames: string[]): boolean {
  return userRoles.some(role => roleNames.includes(role.name))
}

export function isSuperAdmin(userRoles: AdminRole[]): boolean {
  return hasAnyRole(userRoles, ['super_admin'])
}

// Get user with roles
export async function getAdminUserWithRoles(userId: string): Promise<AdminUserWithRoles | null> {
  const supabase = createAdminSupabaseClient()
  
  const { data: profile, error: profileError } = await supabase
    .from('admin_profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (profileError || !profile) {
    return null
  }

  const { data: userRoles, error: rolesError } = await supabase
    .from('admin_user_roles')
    .select(`
      admin_roles (
        id,
        name,
        description,
        permissions,
        created_at,
        updated_at
      )
    `)
    .eq('user_id', userId)

  if (rolesError) {
    return null
  }

  const roles = userRoles
    .map(ur => ur.admin_roles)
    .filter(Boolean) as AdminRole[]

  return {
    ...profile,
    roles
  }
}

// Verify admin access
export async function verifyAdminAccess(
  resource?: string,
  action?: string
): Promise<{ user: AdminUserWithRoles | null; hasAccess: boolean }> {
  const supabase = await createServerSupabaseAuth()

  const { data: { user }, error } = await supabase.auth.getUser()

  if (error || !user) {
    return { user: null, hasAccess: false }
  }

  const adminUser = await getAdminUserWithRoles(user.id)

  if (!adminUser || !adminUser.is_active) {
    return { user: null, hasAccess: false }
  }

  // If no specific resource/action required, just check if user is admin
  if (!resource || !action) {
    return { user: adminUser, hasAccess: adminUser.roles.length > 0 }
  }

  // Check specific permission
  const hasAccess = hasPermission(adminUser.roles, resource, action)

  return { user: adminUser, hasAccess }
}
