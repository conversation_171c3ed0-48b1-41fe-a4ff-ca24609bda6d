'use client';

import { useContext, useEffect, useState } from 'react';
import { createClientSupabaseAuth } from '@/lib/auth';
import { AuthContext } from '@/components/providers/AuthProvider';
import type { User } from '@supabase/supabase-js';

interface AdminRole {
  name: string;
  permissions: Record<string, string[]>;
}

interface AdminUser {
  id: string;
  username: string | null;
  full_name: string | null;
  avatar_url: string | null;
  is_active: boolean;
  last_login_at: string | null;
  roles: AdminRole[];
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook functions for auth context
export function useAuthState() {
  const [user, setUser] = useState<User | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  const supabase = createClientSupabaseAuth();

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user ?? null);

      if (session?.user) {
        await fetchAdminUser();
      }

      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchAdminUser();
        } else {
          setAdminUser(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchAdminUser = async () => {
    try {
      const response = await fetch('/api/auth/user');
      if (response.ok) {
        const data = await response.json();
        setAdminUser(data.user);
      } else {
        setAdminUser(null);
      }
    } catch (error) {
      console.error('Error fetching admin user:', error);
      setAdminUser(null);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data.error || 'Login failed' };
      }

      // The auth state change will be handled by the listener
      return {};
    } catch (error) {
      console.error('Sign in error:', error);
      return { error: 'An unexpected error occurred' };
    }
  };

  const signOut = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const hasPermission = (resource: string, action: string): boolean => {
    if (!adminUser) return false;

    return adminUser.roles.some(role => {
      const resourcePermissions = role.permissions[resource];
      return resourcePermissions && resourcePermissions.includes(action);
    });
  };

  const hasRole = (roleName: string): boolean => {
    if (!adminUser) return false;
    return adminUser.roles.some(role => role.name === roleName);
  };

  const isSuperAdmin = (): boolean => {
    return hasRole('super_admin');
  };

  return {
    user,
    adminUser,
    loading,
    signIn,
    signOut,
    hasPermission,
    hasRole,
    isSuperAdmin,
  };
}
