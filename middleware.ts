import { NextRequest, NextResponse } from 'next/server';
import { createMiddlewareSupabaseAuth, getAdminUserWithRoles } from './lib/auth-server';

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};

// Simple in-memory rate limiter for login attempts
// This would be better implemented with Redis in production
const loginAttempts = new Map<string, { count: number, timestamp: number }>();
const MAX_ATTEMPTS = 5;
const WINDOW_MS = 15 * 60 * 1000; // 15 minutes

export async function middleware(request: NextRequest) {
  // Create response with security headers
  const response = NextResponse.next({
    headers: {
      // Cache control headers
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Surrogate-Control': 'no-store',
      
      // Security headers
      'Content-Security-Policy': 
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://storage.googleapis.com https://*.googletagmanager.com https://*.google-analytics.com; " +
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
        "img-src 'self' data: https: blob:; " +
        "font-src 'self' https://fonts.gstatic.com; " +
        "connect-src 'self' https://*.supabase.co https://soaoagcuubtzojytoati.supabase.co wss://soaoagcuubtzojytoati.supabase.co https://*.googleapis.com https://*.google-analytics.com; " +
        "frame-src 'self' https://www.google.com https://www.youtube.com;",
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self), interest-cohort=()',
      'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload'
    }
  });

  const url = request.nextUrl.clone();
  
  // Rate limit login attempts
  if (url.pathname === '/api/admin/login') {
    const ip = request.headers.get('x-forwarded-for') || 'unknown';
    const now = Date.now();
    
    // Clean up old entries
    Array.from(loginAttempts.entries()).forEach(([key, data]) => {
      if (now - data.timestamp > WINDOW_MS) {
        loginAttempts.delete(key);
      }
    });
    
    const attempts = loginAttempts.get(ip);
    
    if (attempts && attempts.count >= MAX_ATTEMPTS && now - attempts.timestamp < WINDOW_MS) {
      return new NextResponse(
        JSON.stringify({ error: 'Too many login attempts. Please try again later.' }),
        { status: 429, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Update attempts counter
    loginAttempts.set(ip, { 
      count: (attempts?.count || 0) + 1, 
      timestamp: attempts?.timestamp || now 
    });
  }

  // Check if request is for admin routes
  if (url.pathname.startsWith('/admin') && url.pathname !== '/admin/login') {
    try {
      const { supabase, response: authResponse } = createMiddlewareSupabaseAuth(request);

      // Get the current user session
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        // No valid session, redirect to login
        const redirectResponse = NextResponse.redirect(new URL('/admin/login', request.url));
        return redirectResponse;
      }

      // Get admin user with roles
      const adminUser = await getAdminUserWithRoles(user.id);

      if (!adminUser || !adminUser.is_active || adminUser.roles.length === 0) {
        // User is not an admin or is inactive, redirect to login
        const redirectResponse = NextResponse.redirect(new URL('/admin/login', request.url));
        return redirectResponse;
      }

      // Update last login time
      try {
        const adminSupabase = createMiddlewareSupabaseAuth(request).supabase;
        await adminSupabase
          .from('admin_profiles')
          .update({ last_login_at: new Date().toISOString() })
          .eq('id', user.id);
      } catch (updateError) {
        // Non-critical error, just log it
        console.warn('Failed to update last login time:', updateError);
      }

      // Continue with the auth response that may have refreshed tokens
      return authResponse;
    } catch (error) {
      console.error('Error verifying admin session:', error);
      // On error, redirect to login
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
  }

  return response;
} 